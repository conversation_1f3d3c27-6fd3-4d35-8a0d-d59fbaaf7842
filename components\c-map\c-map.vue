<template>
	<view>
		<map style="width: 100%; height: 60vh;" :latitude="latitude" :longitude="longitude" :markers="markers" />
	</view>
</template>

<script>
	var amapFile = require('../../utils/amap-wx.130.js');
	var myAmapFun = new amapFile.AMapWX({key: '37e31a3a5c07fe8b20c1bfdd3a9106f9'});
	export default {
		data() {
			return {
				id: 1, // 使用 marker点击事件 需要填写id
				title: 'map',
				latitude: 30.64,
				longitude: 104.05,
				markers: [{
					latitude: 30.64,
					longitude: 104.05,
					iconPath: '../../static/tabbar/coordinates.png',
					width: 16,
					height: 30,
					callout: {
						content: '武侯区人民政府(武候祠大街南)',
						borderRadius: 10,
						bgColor: '#FF80B5',
						display: 'ALWAYS',
						color: '#fff',
						padding: '5'
					}
				}]
			}
		},
		methods: {

		}
	}
</script>

<style>
</style>