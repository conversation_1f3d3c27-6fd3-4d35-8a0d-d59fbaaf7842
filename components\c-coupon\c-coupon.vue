<template>
	<view class="page">
		<view class="coupon" v-for="v in couponList" :key="v.id">
			<image class="coupon_bg" :src="images.couponImg" mode="" v-if="v.useStatus==1"></image>
			<image class="coupon_bg" :src="images.couponImgA" mode="" v-else></image>

			<view class="detail">
				<view class="left">
					<view class="coupon_price">
						<text class="unit">￥</text><text class="num">{{v.amount?(v.amount/100).toFixed(0):0}}</text>
					</view>
					<view class="unconditional">
						{{v.type==1?'无门槛优惠券':'满减优惠券'}}
					</view>
				</view>
				<view class="right">
					<view class="centent">
						<view class="coupon_name single-line-ellipsis">{{v.name}}</view>
						<view class="city">适用城市：{{JSON.parse(v.cityName).join(',')}}</view>
						<view class="">有效期：{{v.endTime}}</view>
						<view class="direction" @click="useDire(v.description)">使用说明
							<image class="go2" :src="images.go" mode="" />
						</view>
					</view>
					<view class="cou_btn">
						<button v-if="couponType==0" class="use" @click="immediateClaim(v.id)">立即领取</button>
						<button v-else-if="couponType==1&&v.useStatus==0" class="use">待使用</button>
						<button v-else-if="couponType==1&&v.useStatus==2" class="use over_use">已失效</button>
						<button v-else class="use over_use">已使用</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 使用说明 -->
		<u-modal :show="instructionsShow" :title="instructionsTitle" width="560rpx" :content='content'
			confirmButtonShape="circle">
			<view slot="confirmButton">
				<!-- 这里是自定义按钮 -->
				<button class="pinkbtn" @click="instructionsShow=false">确认</button>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import images from './images.js'
	export default {
		name: "couponItem",
		props: {
			couponList: {
				type: Array,
				default: [{
					amount: 200, //优惠券金额(分)
					cityName: '', //适用城市
					description: '', //使用说明
					endTime: '', //失效时间
					id: '', //优惠券id
					minPrice: 0, //满减金额(分)
					name: '优惠券名称',
					type: '', //优惠券类型（1-无门槛 2-满减）
					useStatus: '' //使用状态
				}]
			},
		},
		data() {
			return {
				images,
				instructionsShow: false,
				instructionsTitle: '使用说明',
				couponType: 0,
				content: ''
			};
		},
		onLoad() {},
		onShow() {},
		methods: {
			// 使用说明
			useDire(v) {
				this.content = v
				this.instructionsShow = true
			},

			immediateClaim(id) {
				this.$emit('getNow', id)
			}
		},
	};
</script>

<style lang="scss" scoped>
	.coupon {
		position: relative;
		margin-top: 20rpx;

		.coupon_bg {
			display: block;
			width: 702rpx;
			height: 208rpx;
			margin: 0 auto;
		}

		.detail {
			width: 702rpx;
			height: 208rpx;
			position: absolute;
			top: 0;
			left: 50%;
			transform: translate(-50%);
			display: flex;

			.left {
				width: 189rpx;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.coupon_price {
					color: #18181A;
					font-weight: bold;

					.unit {
						font-size: 32rpx;
					}

					.num {
						font-size: 80rpx;
					}
				}

				.unconditional {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 20rpx;
					color: #18181A;
					line-height: 36rpx;
				}
			}

			.right {
				width: 514rpx;
				height: 100%;
				padding: 20rpx 20rpx 30rpx;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;

				.centent {
					width: 350rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #606066;
					text-align: left;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.coupon_name {
						width: 100%;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 28rpx;
						color: #18181A;
						line-height: 36rpx;
						text-align: left;
					}

					.direction {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 20rpx;
						color: #909099;
						line-height: 36rpx;
						text-align: left;

						.go2 {
							width: 16rpx;
							height: 16rpx;
							margin-left: 5rpx;
						}
					}
				}

				.cou_btn {
					width: 120rpx;
					display: flex;
					align-items: center;

					.use {
						width: 120rpx;
						height: 56rpx;
						background: #FFCCDF;
						border-radius: 30rpx 30rpx 30rpx 30rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 20rpx;
						color: #18181A;
						line-height: 56rpx;
						text-align: center;
						padding: 0;
					}

					.over_use {
						background-color: #E1E1E5;
					}
				}
			}
		}
	}
	
	.city{
		width: 100%; /* 或者其他固定宽度 */
		white-space: nowrap; /* 保持文本在一行显示 */  
		overflow: hidden; /* 隐藏超出容器的部分 */  
		text-overflow: ellipsis; /* 超出部分显示省略号 */  
	}
</style>