// uni方法二次封装

/**
 * @description uni.showToast  Promise化，用.then()代替success里面的setTimeout
 */
export function showToast(params = {}) {
	let obj = {
		duration: 1500,
		title: '请传入{title}',
		icon: 'none',
		mask: true
	}
	// 将参数合并
	let objs = Object.assign(obj, params)
	return new Promise((resolve, reject) => {
		uni.showToast({
			duration: objs.duration,
			title: objs.title,
			icon: objs.icon,
			mask: objs.mask,
			success() {
				let time = setTimeout(() => {
					clearTimeout(time)
					resolve()
				}, objs.duration)
			}
		})
	})
}

/**
 * @description uni.navigateTo  
 * @param String  url 跳转的页面路径
 */
export function jumpPage(url) {
	uni.navigateTo({
		url,
		success(res) {
			// console.log('跳转res',err);
		},
		fail(err) {
			console.log('跳转err', err);
		}
	})
}

/**
 * @description uni.navigateBack  
 */
export function jumpBack() {
	uni.navigateBack()
}

/**
 * 监听是否开启定位授权
 */
export function getSetting() {
	// 检查用户是否已授权定位权限  
	uni.getSetting({
		success: (res) => {
			if (!res.authSetting['scope.userLocation']) {
				// 用户未授权定位权限  
				uni.showModal({
					title: '提示',
					content: '使用该功能需要您的位置信息，是否允许访问？',
					success: (modalRes) => {
						if (modalRes.confirm) {
							// 用户点击了允许，尝试打开设置页面让用户重新授权  
							uni.openSetting({
								success: (settingRes) => {
									if (settingRes.authSetting[
										'scope.userLocation']) {
										// 用户重新授权了定位权限  
										console.log('用户已重新授权定位');
										// 在这里执行需要定位权限的操作，如获取位置等  
									} else {
										// 用户未重新授权定位权限  
										console.log('用户未重新授权定位');
									}
								},
								fail: () => {
									console.log('打开设置页面失败');
								}
							});
						}
					}
				});
			} else {
				// 用户已授权定位权限，直接执行相关操作  
				console.log('用户已授权定位');
			}
		}
	});
}