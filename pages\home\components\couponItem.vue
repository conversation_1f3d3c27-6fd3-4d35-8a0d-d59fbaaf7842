<template>
	<view class="page">
		<view class="coupon">
			<image class="coupon_bg" src="../../../static/tabbar/coupons-a.png" mode=""></image>
			<view class="detail">
				<view class="left">
					<view class="coupon_price">
						<text class="unit">￥</text><text class="num">{{coupon.amount?(coupon.amount/100).toFixed(0):0}}</text>
					</view>
					<view class="unconditional">
						{{coupon.name}}
					</view>
				</view>
				<view class="right">
					<view class="centent">
						<view class="coupon_name single-line-ellipsis">{{coupon.name}}</view>
						<view class="city">适用城市：{{JSON.parse(coupon.cityName).join(',')}}</view>
						<view class="">有效期：{{coupon.endTime}}</view>
						<view class="direction">使用说明 <image class="go2" src="../../../static/common/go2.png" mode="">
							</image>
						</view>
					</view>
					<view class="cou_btn">
						<button class="use" @click="immediateUse(coupon)">立即使用</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "couponItem",
		props: {
			coupon: {
				type: Object,
				default: {
					amount: 20, //优惠券金额(分)
					cityName: '', //适用城市
					description: '', //使用说明
					endTime: '', //失效时间
					id: '', //优惠券id
					minPrice: 0, //满减金额(分)
					name: '优惠券名称',
					type: '', //优惠券类型（1-无门槛 2-满减）
					useStatus: '' //使用状态
				}
			}
		},
		data() {
			return {};
		},
		onLoad() {},
		onShow() {},
		methods: {
			immediateUse(v){
				this.$emit('immediateUse',v)
			}
		}
	};
</script>

<style lang="scss" scoped>
	.coupon {
		position: relative;
		margin-top: 20rpx;

		.coupon_bg {
			display: block;
			width: 702rpx;
			height: 208rpx;
			margin: 0 auto;
		}

		.detail {
			width: 702rpx;
			height: 208rpx;
			position: absolute;
			top: 0;
			left: 50%;
			transform: translate(-50%);
			display: flex;

			.left {
				width: 189rpx;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.coupon_price {
					color: #18181A;
					font-weight: bold;

					.unit {
						font-size: 32rpx;
					}

					.num {
						font-size: 80rpx;
					}
				}

				.unconditional {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 20rpx;
					color: #18181A;
					line-height: 36rpx;
				}
			}

			.right {
				width: 514rpx;
				height: 100%;
				padding: 20rpx 20rpx 30rpx;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;

				.centent {
					width: 350rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #606066;
					text-align: left;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.coupon_name {
						width: 100%;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 28rpx;
						color: #18181A;
						line-height: 36rpx;
						text-align: left;
					}

					.direction {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 20rpx;
						color: #909099;
						line-height: 36rpx;
						text-align: left;

						.go2 {
							width: 16rpx;
							height: 16rpx;
							margin-left: 5rpx;
						}
					}
				}

				.cou_btn {
					width: 120rpx;
					display: flex;
					align-items: center;

					.use {
						width: 120rpx;
						height: 56rpx;
						background: #FFCCDF;
						border-radius: 30rpx 30rpx 30rpx 30rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 20rpx;
						color: #18181A;
						line-height: 56rpx;
						text-align: center;
						padding: 0;
					}
				}
			}
		}
	}
	.city{
		width: 100%; /* 或者其他固定宽度 */
		white-space: nowrap; /* 保持文本在一行显示 */  
		overflow: hidden; /* 隐藏超出容器的部分 */  
		text-overflow: ellipsis; /* 超出部分显示省略号 */  
	}
</style>