<template>
	<!-- tab切换 -->
	<view class="">
		<view class="tab">
			<view class="ta-box" v-for="(v,i) in dataList" :key="i">
				<view class="ta-title" @click="changeTab(v,i)">
					<text class="ta-text" :class="{active:tabListIndex==i}">{{v.name}}</text>
				</view>
				<!-- <view class="ta-line"></view> -->
			</view>

		</view>
		<view class="segmentation"></view>
	</view>


</template>

<script>
	export default {
		props: {
			tabIndex: {
				type: String | Number,
				default: 0
			},
			dataList: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				tabListIndex: this.tabIndex
			}
		},
		methods: {
			// 切换消息类型
			async changeTab(val, inx) {
				this.tabListIndex = inx
				this.$emit('change', val, inx)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tab {
		display: flex;
		align-items: center;
		justify-content: space-around;
		text-align: center;
		padding: 24rpx 0 10rpx;
		border-bottom: 1rpx solid #DDDDE6;
		// background: #fff;
		position: sticky;
		top: 0;

		.ta-box {
			display: flex;
			align-items: center;
			flex: 1;

			&:nth-last-child(1) {
				.ta-line {
					width: 0;
				}
			}

			.ta-line {
				width: 1rpx;
				height: 24rpx;
				background: #C8CACC;
				border-radius: 0rpx;

			}

			.ta-title {
				flex: 1;
				font-size: 28rpx;
				font-weight: 500;
				color: #606066;


				.ta-text {
					&.active {
						font-size: 30rpx;
						position: relative;
						font-weight: 800;
						font-size: 32rpx;
						color: #18181A;




						&::after {
							width: 100%;
							position: absolute;
							display: inline-block;
							content: '';
							bottom: -12rpx;
							left: 50%;
							transform: translateX(-50%);
							height: 10rpx;
							background: $c-bgColor;
							border-radius: 20rpx;
							z-index: -1;
						}
					}
				}

			}
		}

	}
</style>