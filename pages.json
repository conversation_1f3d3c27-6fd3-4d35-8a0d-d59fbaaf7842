{
    "easycom": {
        "^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue",
        "^c-(.*)": "@/components/c-$1/c-$1.vue"
    },
    "pages": [
        //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
            "path": "pages/tabbar/home",
            "style": {
                "navigationBarTitleText": " "
            }
        },
        {
            "path": "pages/tabbar/orderList",
            "style": {
                "navigationBarTitleText": "订单",
                "navigationBarBackgroundColor": "#ffffff"
            }
        },
        {
            "path": "pages/tabbar/mine",
            "style": {
                "navigationBarTitleText": "个人中心",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabbar/login",
            "style": {
                "navigationBarTitleText": "登录"
            }
        },
        {
            "path": "pages/tabbar/agreement",
            "style": {
                "navigationBarTitleText": "",
                "enablePullDownRefresh": false
            }
        }
    ],
    "subPackages": [
        {
            "root": "pages/home",
            "pages": [
                {
                    "path": "pages/fillInInformation",
                    "style": {
                        "navigationBarTitleText": "填写信息",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/selectAddress",
                    "style": {
                        "navigationBarTitleText": "选择地址",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/confirmOrder",
                    "style": {
                        "navigationBarTitleText": "确认下单",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/selectCoupon",
                    "style": {
                        "navigationBarTitleText": "选择优惠券",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/payOrder",
                    "style": {
                        "navigationBarTitleText": "订单支付",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/paymentSuccess",
                    "style": {
                        "navigationBarTitleText": "支付成功",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/feedingOrder",
                    "style": {
                        "navigationBarTitleText": "订单详情"
                    }
                },
                {
                    "path": "pages/selectPet",
                    "style": {
                        "navigationBarTitleText": "选择宠物",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/selectService",
                    "style": {
                        "navigationBarTitleText": "选择服务",
                        "navigationStyle": "custom"
                    }
                }
            ]
        },
        {
            "root": "pages/orderList",
            "pages": [
                {
                    "path": "pages/orderDetail",
                    "style": {
                        "navigationBarTitleText": "订单详情",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/orderDetail2",
                    "style": {
                        "navigationBarTitleText": "订单详情",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/orderCancelSuccess",
                    "style": {
                        "navigationBarTitleText": "取消订单成功",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/driverPosition",
                    "style": {
                        "navigationBarTitleText": "司机位置",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/driverVideo",
                    "style": {
                        "navigationBarTitleText": "司机视频"
                    }
                }
            ]
        },
        {
            "root": "pages/my",
            "pages": [
                {
                    "path": "pages/information",
                    "style": {
                        "navigationBarTitleText": "个人中心",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/service",
                    "style": {
                        "navigationBarTitleText": "客服中心",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/settings",
                    "style": {
                        "navigationBarTitleText": "设置",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/changePass",
                    "style": {
                        "navigationBarTitleText": "忘记密码",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/address",
                    "style": {
                        "navigationBarTitleText": "常用地址",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/editAddress",
                    "style": {
                        "navigationBarTitleText": "新增地址",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/topUp",
                    "style": {
                        "navigationBarTitleText": "充值",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/rechargeSuccess",
                    "style": {
                        "navigationBarTitleText": "充值成功",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/rechargeRecord",
                    "style": {
                        "navigationBarTitleText": "充值记录",
                        "enablePullDownRefresh": false,
                        "navigationBarBackgroundColor": "#fff"
                    }
                },
                {
                    "path": "pages/flowRecord",
                    "style": {
                        "navigationBarTitleText": "流水记录",
                        "enablePullDownRefresh": false
                    }
                },
                {
                    "path": "pages/myCoupon",
                    "style": {
                        "navigationBarTitleText": "我的优惠券",
                        "enablePullDownRefresh": false,
                        "navigationBarBackgroundColor": "#fff"
                    }
                },
                {
                    "path": "pages/myDistribution",
                    "style": {
                        "navigationBarTitleText": "我的分销"
                    }
                },
                {
                    "path": "pages/withdrawMoney",
                    "style": {
                        "navigationBarTitleText": "提现"
                    }
                },
                {
                    "path": "pages/withdrawRecords",
                    "style": {
                        "navigationBarTitleText": "流水记录"
                    }
                },
                {
                    "path": "pages/petManagement",
                    "style": {
                        "navigationBarTitleText": "宠物管理",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/addPet",
                    "style": {
                        "navigationBarTitleText": "添加宠物",
                        "navigationStyle": "custom"
                    }
                }
            ]
        }
    ],
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "uni-app",
        "navigationBarBackgroundColor": "#F8F8F8",
        "backgroundColor": "#F8F8F8"
    },
    "uniIdRouter": {},
    // 不需要导航栏的话就删除
    "tabBar": {
        "color": "#5C5E66",
        "selectedColor": "#FF7BAC",
        "backgroundColor": "#FFFFFF",
        "borderStyle": "black",
        // list里面写图标和文字
        "list": [
            {
                "selectedIconPath": "/static/tabbar/home-a.png",
                "iconPath": "/static/tabbar/home.png",
                "pagePath": "pages/tabbar/home",
                "text": "首页"
            },
            {
                "selectedIconPath": "/static/tabbar/example-a.png",
                "iconPath": "/static/tabbar/example.png",
                "pagePath": "pages/tabbar/orderList",
                "text": "订单"
            },
            {
                "selectedIconPath": "/static/tabbar/mine-a.png",
                "iconPath": "/static/tabbar/mine.png",
                "pagePath": "pages/tabbar/mine",
                "text": "我的"
            }
        ]
    }
}
