<template>
	<view class="page">
		<view class="headBg">
			<c-navBar title="填写信息" isTran isPerch :background="ceiling?'rgb(253, 220, 232)':''"></c-navBar>
			<!-- 新建地址 -->
			<view class="newAddress">
				<u--form labelPosition="left" :model="params" labelWidth="60" :rules="rules" ref="uForm">
					<u-form-item label="地址" borderBottom prop="address" ref="">
						<view class="new_item" @click="$fn.jumpPage('./selectAddress')">
							<view class="centent" :class="params.address?'haveadd':'noadd'">
								{{params.address?params.address:'选择地址'}}
							</view>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<u-form-item label="门牌号" borderBottom prop="house" ref="">
						<view class="new_item">
							<u--input placeholderClass="input-placeholder" placeholder="单元、楼层、门牌号等，如:2单元301"
								border="none" v-model="params.house"></u--input>
						</view>
					</u-form-item>

					<u-form-item label="联系人" borderBottom prop="name" ref="">
						<view class="new_item">
							<u--input placeholderClass="input-placeholder" placeholder="联系人姓名" border="none"
								v-model="params.name"></u--input>
						</view>
					</u-form-item>

					<u-form-item label="电话" borderBottom prop="phone" ref="">
						<view class="new_item">
							<u--input placeholderClass="input-placeholder" placeholder="联系人电话" border="none"
								v-model="params.phone"></u--input>
						</view>
					</u-form-item>
				</u--form>

				<button class="pinkbtn mt40" @click="save">保存并使用</button>
			</view>

			<!-- 我的地址 -->

			<view class="myaddress">

				<view class="title">我的地址</view>
				<view class="address_item" v-for="item in addressList" :key="item.id">
					<view class="surname">
						{{item.name.charAt(0)}}
					</view>
					<view class="content">
						<view class="info" @click="useAddress(item)">
							<view class="address_name single-line-ellipsis">
								{{item.address}}
							</view>
							<view class="address_name single-line-ellipsis">
								{{item.house}}
							</view>
							<view class="user">
								<text class="username">{{item.name}}</text> <text>{{item.phone}}</text>
							</view>
						</view>

						<view class="btns">
							<text class="btn1" @click="jumpEditAddress(item)">编辑</text>
							<text class="btn2" @click="delAddressFn(item.id)">删除</text>
						</view>

					</view>
				</view>

				<view v-if="addressList.length==0" class="no-data">
					<image src="/static/common/noContent.png" mode=""></image>
					无收货地址
				</view>

			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				addressList: [],
				params: {
					address: "", //地址
					house: "", //纬度
					lat: 0, //纬度
					lng: 0, //经度
					name: "", //联系人
					phone: "", //联系电话
					type: null, //1-起点 2-终点
				},
				rules: {
					address: {
						type: 'string',
						required: true,
						message: '请选择地址',
						trigger: ['blur', 'change']
					},
					house: {
						type: 'string',
						required: true,
						message: '请输入门牌号',
						trigger: ['blur', 'change']
					},
					name: {
						type: 'string',
						required: true,
						message: '请输入联系人',
						trigger: ['blur', 'change']
					},
					phone: [{
							type: 'string',
							required: true,
							message: '请输入手机号',
							trigger: ['blur', 'change']
						},
						{
							type: 'string',
							pattern: /^((13[0-9])|(14[0-9])|(15[0-9])|(16[2-7])|(17[0-8])|(18[0-9])|(19[0-9]))\d{8}$/,
							message: '请输入正确手机号',
							trigger: ['blur', 'change']
						}
					]

				},
				ceiling: false,
			};
		},
		onPageScroll(e) {
			console.log(e, '滚动距离');
			if (e.scrollTop > 0) {
				this.ceiling = true
			} else {
				this.ceiling = false
			}
		},
		onLoad(e) {
			if (e.type) {
				this.params.type = e.type
			}
		},
		onShow() {
			// 获取传回来的本地地址信息
			this.getAddressListFn(this.params.type)
			this.getAddress()
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules)
		},
		methods: {
			// 获取传回来的本地地址信息
			getAddress() {
				const addressInfo = uni.getStorageSync('address')
				if (addressInfo) {
					console.log(addressInfo,'ssss')
					this.params.address = addressInfo.district + addressInfo.name
					this.params.lng = addressInfo.location.split(',')[0]
					this.params.lat = addressInfo.location.split(',')[1]
					uni.removeStorageSync('address')
				}
			},

			// 获取地址列表
			getAddressListFn() {
				this.$api.getAddressList({
					type: this.params.type
				}).then(res => {
					this.addressList = res.data
					console.log(res, '获取地址列表');
				})
			},

			// 删除地址列表
			delAddressFn(id) {
				uni.showModal({
					title: '温馨提示',
					content: '确认删除此地址?',
					success: (res) => {
						if (res.confirm) {
							// console.log('用户点击确定');
							this.$api.delAddress({
								id
							}).then(res => {
								uni.showToast({
									title: '操作成功',
									icon: 'success'
								})
								this.getAddressListFn()
							})
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},

			// 保存并使用
			save() {
				this.$refs.uForm.validate().then(res => {
					// uni.$u.toast('校验通过')
					console.log(this.params, 'this.params参数');
					this.$api.editAddress(this.params).then(res => {
						console.log(res, '保存成功');
						this.useAddress(this.params)
					})
				}).catch(errors => {
					// uni.$u.toast('校验失败')
				})
			},
			// 跳转到编辑地址
			jumpEditAddress(v) {
				uni.navigateTo({
					url: '/pages/my/pages/editAddress?v=' + JSON.stringify(v)
				})
			},

			//使用地址
			useAddress(v) {
				if (this.params.type == '1') {
					uni.setStorageSync('pickAddress', v)
				} else {
					console.log('送货地址')
					uni.setStorageSync('sendAddress', v)
				}
				uni.navigateBack()
			}
		}
	};
</script>

<style lang="scss" scoped>
	.mt40 {
		margin-top: 40rpx;
	}

	.page {
		min-height: 100vh;
		background: #F4F5F7;
	}

	.headBg {
		width: 750rpx;
		height: 320rpx;
		background: linear-gradient(360deg, rgba(255, 204, 223, 0) 0%, #FFD9E7 100%);
	}

	.newAddress {
		width: 702rpx;
		background: #FFFFFF;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		margin: 10rpx auto;
		padding: 0 24rpx 24rpx;
		box-sizing: border-box;

		.new_item {
			width: 100%;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			// background-color: red;
			// border-bottom: 1rpx solid #F0F0F0;

			.title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 32rpx;
				color: #18181A;
				line-height: 38rpx;
			}

			.centent {
				width: 500rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				// line-height: 28rpx;
				text-align: left;
				// background-color: #56B7F5;
				white-space: nowrap;
				/* 保持文本在一行显示 */
				overflow: hidden;
				/* 隐藏超出容器的部分 */
				text-overflow: ellipsis;
				/* 超出部分显示省略号 */
			}

			.noadd {
				font-size: 24rpx;
				color: #909099;
			}

			.haveadd {
				color: #303133;
				font-size: 24rpx;
			}

			.icon {
				width: 10%;
				// background-color: rosybrown;
				text-align: right;

				.img_ic {
					width: 12rpx;
					height: 20rpx;
				}

			}

		}
	}


	// 输入框样式穿透
	::v-deep.input-placeholder {
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #909099 !important;
		line-height: 28rpx;
		text-align: left;
	}

	.myaddress {
		width: 702rpx;
		min-height: 640rpx;
		background: #FFFFFF;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		margin: 20rpx auto;
		padding: 24rpx;
		box-sizing: border-box;

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #18181A;
			line-height: 42rpx;
		}

		.address_item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 130rpx;

			.surname {
				width: 48rpx;
				height: 48rpx;
				background: #FFCCDF;
				border-radius: 50%;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 24rpx;
				color: #18181A;
				line-height: 48rpx;
				text-align: center;
			}

			.content {
				width: 88%;
				height: 130rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid #F0F0F0;

				.info {
					width: 80%;

					.address_name {
						width: 100%;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 28rpx;
						color: #18181A;
						line-height: 33rpx;
					}

					.user {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #606066;
						line-height: 28rpx;
						margin-top: 10rpx;

						.username {
							margin-right: 30rpx;
						}
					}
				}

				.btns {
					width: 20%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					line-height: 28rpx;

					.btn1 {
						color: #56B7F5;
					}

					.btn2 {
						color: #FF7BAC;
					}
				}
			}
		}
	}

	.no-data {
		margin: 160rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		color: #BCBCBC;
		font-size: 28rpx;

		image {
			width: 300rpx;
			height: 140rpx;
		}
	}
</style>