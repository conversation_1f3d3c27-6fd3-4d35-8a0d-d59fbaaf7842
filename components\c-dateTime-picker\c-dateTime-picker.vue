<template>
	<!-- 日期时间组件 -->
	<view class="page">
		<u-picker :show="show" title="选择预约时间" ref="uPicker" keyName="label" :columns="columns" @confirm="confirm"
			@cancel="cancel" @change="changeHandler" immediateChange :defaultIndex="defaultIndex"></u-picker>
	</view>
</template>

<script>
	export default {
		name: "c-dateTime-picker",
		props: {
			show: {
				type: Boolean,
				default: false
			},
			city: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				columns: [
					[],
					[],
					[]
				],
				columnData: [
					[],
					[],
					[],
				],
				//	时间限制
				timeLimit: {
					afterTime: 0,
					currentEarliestOrderTime: 0,
					earliestOrderTime: '',
					latestOrderTime: ''
				},
				hourLimit: [],
				defaultIndex: []
			};
		},
		mounted() {
			this.getCityOrderTimeFn()
		},
		methods: {
			changeHandler(e) {
				const {
					columnIndex,
					index,
					value,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				// picker.setIndexs[1] = 0
				if (columnIndex === 0) {
					if (new Date(value[0].value).getTime() > new Date().getTime()) {
						this.getOrderTimeStatusFn(this.city, new Date(value[0].value), false)
						picker.setColumnValues(1, this.columnData[1])
					} else {
						picker.setColumnValues(1, this.columns[1])
					}
				}
				console.log(columnIndex, '这是第几列');
				if (columnIndex === 0 || columnIndex === 1) {
					this.setColumnTow(value)
					picker.setColumnValues(2, this.columnData[2])
				}
			},

			//获取当前城市可下单的时间信息
			getCityOrderTimeFn() {

				this.$api.getCityOrderTime({
					city: this.city
				}).then(res => {
					console.log(res, '获取当前城市可下单的时间信息');
					this.timeLimit = res.data
					this.addColumns1()
					this.getOrderTimeStatusFn(this.city, new Date(), true)
				})
			},
			//获取当前城市当前日期下单的梯度时间状态
			getOrderTimeStatusFn(city, date, setIndex) {
				console.log(this.city, 'this.city');
				console.log(date, 'date');
				this.$api.getOrderTimeStatus({
					city,
					date
				}).then(res => {
					// console.log(date.getDate(), '今天的日期');
					console.log(res, '今天需要限制的时间');
					if (res.data.hour.length > 0) {
						this.hourLimit = res.data.hour.map(v => {
							return Number(v.slice(11, 13))
						})
					} else {
						this.hourLimit = []
					}

					this.addColumns(setIndex)
					console.log(this.hourLimit, 'this.hourLimit');
				})
			},

			// 添加时间
			addColumns(setIndex) {

				// 添加小时
				let bool = Number(this.timeLimit.earliestOrderTime.slice(0, 2)) > (new Date(Number(this.timeLimit
					.currentEarliestOrderTime)).getHours())
				let hour1 = []
				let hour2 = []
				for (let i = 0; i < 24; i++) {
					hour1.push({
						label: i + '点' + (this.hourLimit.includes(i) ? '(已满)' : ''),
						value: i < 10 ? '0' + i : i,
						disabled: this.hourLimit.includes(i) || !this.isDisabled(i, bool, 1)
					})
					hour2.push({
						label: i + '点' + (this.hourLimit.includes(i) ? '(已满)' : ''),
						value: i < 10 ? '0' + i : i,
						disabled: this.hourLimit.includes(i) || !this.isDisabled(i, bool, 2)
					})
				}
				this.columns[1] = hour1
				this.columnData[1] = hour2
				const hourIndex = this.columns[1].findIndex(v => Number(v.value) == new Date(Number(this.timeLimit
					.currentEarliestOrderTime)).getHours())
				const miIndex = this.columns[2].findIndex(v => Number(v.value) == new Date(Number(this.timeLimit
					.currentEarliestOrderTime)).getMinutes())

				if (setIndex) {
					this.$nextTick(() => {
						this.defaultIndex = [0, hourIndex, miIndex]
					})
				}

			},


			addColumns1() {
				// 添加日期
				this.columns[0] = this.getDatesForNextTwoYears()
				this.columnData[0] = this.getDatesForNextTwoYears()
				// 添加分钟
				let columns = []
				for (let i = 0; i < 60; i++) {
					columns.push({
						label: i + '分',
						value: i,
						disabled: this.isMinDisabled(i)
					})
				}
				// this.$set(this.columns, 2, columns)
				// this.$set(this.columnData, 2, columns)
				this.columns[2] = columns
				this.columnData[2] = columns
			},

			isDisabled(i, bool, type) {
				if (type == 1) {
					if (bool) {
						return i >= Number(this.timeLimit.earliestOrderTime.slice(0, 2)) &&
							i <= Number(this.timeLimit.latestOrderTime.slice(0, 2))
					} else {
						return i >= new Date(Number(this.timeLimit.currentEarliestOrderTime)).getHours() &&
							i <= Number(this.timeLimit.latestOrderTime.slice(0, 2))
					}
				} else {
					return i >= Number(this.timeLimit.earliestOrderTime.slice(0, 2)) &&
						i <= Number(this.timeLimit.latestOrderTime.slice(0, 2))
				}

			},


			// 设置分钟
			setColumnTow(v) {
				console.log(v, '当前选的时间');
				let day = new Date(v[0].value).getDate()
				let newDay = new Date().getDate()
				let hours = v[1].value
				let newHours = new Date(Number(this.timeLimit.currentEarliestOrderTime)).getHours()


				let columns = []
				if (day == newDay && hours == newHours) {
					for (let i = 0; i < 60; i++) {
						columns.push({
							label: i + '分',
							value: i,
							disabled: this.isMinDisabled(i)
						})
					}
				} else if (day == newDay && hours < newHours) {
					for (let i = 0; i < 60; i++) {
						columns.push({
							label: i + '分',
							value: i,
							disabled: true
						})
					}
				} else {
					for (let i = 0; i < 60; i++) {
						columns.push({
							label: i + '分',
							value: i,
							disabled: false
						})
					}
				}
				this.columnData[2] = columns
			},

			//处理当前分钟前不需要选择
			isMinDisabled(i) {
				return i < new Date(Number(this.timeLimit.currentEarliestOrderTime)).getMinutes()
			},

			// 处理日期数组
			getDatesForNextTwoYears() {
				const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
				const currentDate = new Date(); // 当前日期
				const endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, currentDate
					.getDate()); // 两年后的日期
				const dateArray = [];
				const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

				while (currentDate <= endDate) {
					const month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
					const date = currentDate.getDate();
					const day = weekDays[currentDate.getDay()];
					dateArray.push({
						label: `${month}月${date}日 ${day}`,
						value: currentDate.getTime()
					});
					currentDate.setTime(currentDate.getTime() + oneDay); // 日期加1天
				}

				return dateArray;
			},

			// 点击取消
			cancel() {
				this.$emit('update:show', false)
			},
			// 回调参数为包含columnIndex、value、values
			confirm(e) {
				console.log('confirm', e)
				// 如果某一项有禁用就无法选择
				if (!e.value.every(v => !v.disabled)) return uni.showToast({
					title: '当前时间不可选',
					mask: true,
					icon: 'error'
				})

				let date = {
					label: e.value[0].label + ' ' + e.value[1].value + ':' + (e.value[2].value<10?'0'+e.value[2].value:e.value[2].value),
					value: e.value[0].value + ' ' + e.value[1].value + ':' + (e.value[2].value<10?'0'+e.value[2].value:e.value[2].value)
				}
				// console.log(date, '选择时间');
				this.$emit('confirm', date)
				this.cancel()
			},
		}
	};
</script>

<style lang="scss" scoped>

</style>