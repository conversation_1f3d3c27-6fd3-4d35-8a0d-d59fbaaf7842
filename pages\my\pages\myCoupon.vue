<template>
	<view class="page">
		<view class="main">
			<view class="tab_box">
				<u-tabs :list="tabList" lineWidth="30" lineHeight="4" lineColor="#FF7BAC" :scrollable="false"
					:activeStyle="{color: '#303133',fontWeight: 'bold',transform: 'scale(1.05)'}"
					:inactiveStyle="{color: '#606266',transform: 'scale(1)'}"
					itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;" @change="tabChange">
				</u-tabs>
			</view>
			<view class="coup">
				<c-scroll-list ref="list" :api="api" :option='{auto:false}' :apiParams="apiParams" @load="load">
					<c-coupon ref="coupon" @getNow="getNow" :couponList="couponList"></c-coupon>
				</c-scroll-list>
			</view>

		</view>

		<!-- 领取成功 -->
		<u-modal :show="claimSuccessShow" width="480rpx">
			<view slot="default">
				<view class="content">
					<image class="content_img" src="../static/paySuccessIcon.png" mode=""></image>
					<view class="content_title">领取成功</view>
					<view class="content_detail">已发放至我的优惠券处</view>
				</view>
			</view>
			<view slot="confirmButton">
				<!-- 这里是自定义按钮 -->
				<button class="pinkbtn" @click="claimSuccessShow=false">确认</button>
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		name: "Name",
		data() {
			return {
				claimSuccessShow: false,
				tabList: [{
						name: '领券中心',
					},
					{
						name: '我的优惠券',
					}
				],
				api: this.$api.getCouponList,
				apiParams: {
					city: ''
				},
				couponList: [],
				tabindex: 0,
			};
		},
		onLoad() {
			this.getLocationFn()
			
		},
		onShow() {},
		methods: {

			// 获取定位
			getLocationFn() {
				uni.getLocation({
					type: 'gcj02',
					altitude: true,
					isHighAccuracy: true,
					success: (res) => {
						console.log(res, '当前位置');
						let location = [res.longitude, res.latitude].join(',')
						console.log(location, 'location');
						this.$amapFun.getRegeo({
							location,
							success: (data) => {
								//成功回调
								console.log(data, '我的位置信息');
								this.apiParams.city = data[0].regeocodeData.addressComponent.city
								this.tabChange({
									index: 0
								})
							},
							fail: (info) => {
								//失败回调
								console.log(info)
								if (info.errCode == '10044') {
									uni.showToast({
										icon: 'none',
										title: '获取定位超过限制'
									})
								}
							}
						})
					},
					fail(err) {
						console.log('我没办法获取定位', err);
					}
				});
			},


			tabChange(e) {
				this.couponList = []
				this.$refs.coupon.couponType = e.index
				if (e.index == 0) {
					this.$refs.list.nowApi = this.$api.getCouponList
				} else {
					this.$refs.list.nowApi = this.$api.getMyCouponList
				}
				this.$refs.list.refresh()
			},
			load(res) {
				console.log(res, '优惠券');
				this.couponList = res.list
			},
			// 立即领取
			getNow(couponId) {
				this.$api.receiveCoupon({
					couponId
				}).then(res => {
					this.claimSuccessShow = true
					setTimeout(() => {
						this.$refs.list.initList()
					})
				})

			}

		}
	};
</script>

<style lang="scss" scoped>
	.page {
		height: 100vh;
		background-color: #F4F5F7;
	}

	.main {
		display: flex;
		flex-direction: column;
	}

	.coup {
		flex: 1;
		overflow: scroll;
	}

	.tab_box {
		width: 750rpx;
		background: #FFFFFF;
		padding: 30rpx 0 0;
	}

	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 30rpx;

		.content_img {
			width: 180rpx;
			height: 180rpx;
		}

		.content_title {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #18181A;
			line-height: 42rpx;
		}

		.content_detail {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #606066;
			line-height: 33rpx;
		}
	}
</style>