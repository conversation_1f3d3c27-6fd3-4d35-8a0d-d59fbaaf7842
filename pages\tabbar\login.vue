<template>
	<!-- 登录 -->
	<view>
		<view class="tac flex flex-col flex-center ">
			<image class="logo" src="/static/tabbar/loginLog.png" mode=""></image>
			<!-- <view class="tips">项目名称</view> -->
			<!-- 微信一键登录 -->
			<view>
				<button v-if="!isAgree" class="wx-login" @click="judgmentIsAgree">一键登录</button>
				<button v-else-if="needWechatPhone" open-type="getPhoneNumber" class="wx-login"
					@getphonenumber="wechatLogin">一键登录</button>
				<button v-else class="wx-login" @click="wechatLogin">一键登录</button>
			</view>
			<!-- 微信头像昵称登录 -->
			<view>
				<button class="wx-login" @click="changeShowLogin()">头像昵称登录</button>
			</view>
			<view class="agreement-box">
				<view class="ag-box" @click="tabAgree">
					<image v-if="isAgree" src="/static/common/select.png" mode=""></image>
					<image v-else src="/static/common/noSelect.png" mode=""></image>
				</view>
				<view class="agreement">
					<text @click.stop="tabAgree">我已阅读并同意</text>
					<text class="primary-color" @click.stop="jumpPage(1)">《用户协议》</text>
					<text class="primary-color" @click.stop="jumpPage(2)">《隐私协议》</text>
				</view>
			</view>
		</view>



		<u-popup :show="showLogin" @close="showLogin = false" mode='bottom' :round='20' :closeable='true'>
			<view class="popu">
				<view class="popu-top">
					登录信息
				</view>
				<view class="popu-main">
					<button class="u-reset-button" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">
					</button>
					<button class="u-reset-button avatar" type="balanced" open-type="chooseAvatar"
						@chooseavatar="onChooseAvatar">
						<view class="popu-box">
							<view class="left">
								头像
							</view>
							<view class="right">
								<image class="avatar" :src="formData.avatarUrl"></image>
							</view>
						</view>
					</button>
					<view class="popu-box">
						<view class="left">
							昵称
						</view>
						<view class="right">
							<u--input type="nickname" border="none" :clearable='false' v-model="formData.nickName"
								class="weui-input" placeholder="请输入昵称" input-align='right' />
						</view>
					</view>
				</view>
				<view class="popu-footer" @click="wechatNameLogin">
					登录
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				count: '',
				show: true,
				account: '',
				timer: '',
				pass: '',
				needWechatPhone: false,
				resData: '',
				isAgree: false,
				loginCode: '',
				showLogin: false,
				formData: {
					phone: '',
					avatarUrl: '',
					nickName: '',
				}
			}
		},
		created() {
			this.creatLogin();
		},
		methods: {
			// 同意/不同意协议
			tabAgree() {
				this.isAgree = !this.isAgree
			},
			// 初始登录获取openid
			creatLogin() {
				const that = this
				uni.login({
					provider: 'weixin', //使用微信登录
					success(loginRes) {
						that.$api.loginWeixin({
							loginCode: loginRes.code
						}).then(res => {
							console.log(res, 'res');
							that.loginCode = loginRes.code
							that.resData = res
							if (res.code == 200) {
								console.log(res);
								that.needWechatPhone = res.data == "false"
								console.log(that.needWechatPhone, "是否需要获取手机号")
								// that.loginSuccess(res)
							}
						})
					},
				})
			},

			// 判断是否加密
			judgmentIsAgree() {
				if (!this.isAgree) {
					return uni.showToast({
						title: '请先查看并同意协议',
						icon: 'none',
						duration: 2000
					});
				}
			},


			// 登录成功
			loginSuccess(res) {
				if (!res) {
					this.creatLogin()
					return
				}

				if (!this.isAgree) {
					return uni.showToast({
						title: '请先查看并同意协议',
						icon: 'none',
						duration: 2000
					});
				}
				if (res.code == 200) {
					uni.showToast({
						title: '登录成功！',
						icon: 'none',
						duration: 2000
					});
					this.$u.vuex('vuex_token', res.data.accessToken);
					uni.setStorageSync('token', res.data.accessToken);
					uni.setStorageSync('user', res.data.detail);
					//删除本地存储的邀请用户id
					uni.removeStorageSync('inviteUserId')
					let url = '/pages/tabbar/home'
					setTimeout(() => {
						uni.reLaunch({
							url
						})
					}, 500)
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			},

			changeShowLogin() {
				if (!this.isAgree) {
					return uni.showToast({
						title: '请先查看并同意协议',
						icon: 'none',
						duration: 2000
					});
				}
				this.showLogin = true
			},
			onGetPhoneNumber(e) {

				console.log(e, '手机号码');
				console.log('执行拉去手机号了');
				this.formData.phone = e
			},
			onChooseAvatar(e) {
				console.log(e);
				this.formData.avatarUrl = e.detail.avatarUrl
			},
			// 跳转到协议
			jumpPage(type) {
				this.isAgree = true
				uni.navigateTo({
					url: `/pages/tabbar/agreement?type=${type}`
				})
			},
			// 微信一键登录
			wechatLogin(e) {
				console.log(e, "登录获取的信息")
				if (!this.isAgree) {
					return uni.showToast({
						title: '请先查看并同意协议',
						icon: 'none',
						duration: 2000
					});
				}
				if (e.detail.errMsg == "getPhoneNumber:fail user deny") {
					uni.showToast({
						title: '用户拒绝授权',
						icon: 'none',
						duration: 2000
					});
					return
				}
				const that = this
				if (e) {
					const from = {
						phoneCode: e.detail.code,
						inviteUserId: null,
						loginCode: null,
					}
					uni.login({
						provider: 'weixin', //获取微信登录code
						success(loginRes) {
							console.log(loginRes, "获取微信登录code")
							from.loginCode = loginRes.code
							const local = uni.getStorageSync('inviteUserId')
							console.log(local, "本地获取的邀请用户id")
							if (local) {
								from.inviteUserId = local
							}
							console.log(from, "登录传参")
							that.$api.loginWechat(from).then(res2 => {
								that.loginSuccess(res2)
							})
						},
					})
				}else{
					const from = {
						inviteUserId: null,
						loginCode: null,
					}
					uni.login({
						provider: 'weixin', //获取微信登录code
						success(loginRes) {
							console.log(loginRes, "获取微信登录code")
							from.loginCode = loginRes.code
							const local = uni.getStorageSync('inviteUserId')
							console.log(local, "本地获取的邀请用户id")
							if (local) {
								from.inviteUserId = local
							}
							console.log(from, "登录传参")
							that.$api.loginWechat(from).then(res2 => {
								that.loginSuccess(res2)
							})
						},
					})
				}
			},
			wechatNameLogin() {
				this.showLogin = false
			},
		},

	}
</script>

<style lang="scss" scoped>
	.logo {
		margin-top: 180rpx;
		text-align: center;
		width: 400rpx;
		height: 374rpx;
		// border-radius: 50%;
	}

	.title {
		margin-top: 56rpx;
		font-size: 48rpx;
		font-weight: bold;
		color: #020D1A;
	}

	.tips {
		margin-top: 20rpx;
		font-size: 36rpx;
		font-weight: 500;
		color: #1A1A1A;

	}

	.wx-login {
		margin-top: 80rpx;
		width: 640rpx;
		height: 96rpx;
		background: $c-bgColor;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		opacity: 1;
		font-size: 36rpx;
		color: #FFFFFF;
		display: flex;
		justify-content: center;
		align-items: center;

		.weixin {
			width: 48rpx;
			height: 46rpx;
			margin-right: 12rpx;
		}
	}

	.ple-login {
		padding: 40rpx 0rpx 85rpx;
		text-align: left;

		.inp-box {
			padding: 60rpx 0rpx 30rpx;
			margin: 0 70rpx;
			border-bottom: 1rpx solid #D8DEE6;
			position: relative;

			.phe-icon {
				position: absolute;
				top: 66rpx;
				left: 0;
				width: 28rpx;
				height: 28rpx;
			}

			input {
				padding-left: 60rpx;
			}

		}

		.login {
			margin-top: 100rpx;
			width: 640rpx;
			height: 96rpx;
			background: #04C15F;
			border-radius: 20rpx;
			color: #fff;
			font-size: 36rpx;
			font-weight: bold;
			color: #FFFFFF;

			&.unsatisfied {
				background: #E3E3E3;
				color: #9DA0A5;
			}
		}

		.tab {
			margin-bottom: 75rpx;
		}
	}

	.tab {
		margin-top: 40rpx;
		margin-bottom: 500rpx;
		text-align: center;
		font-size: 28rpx;
		color: #888990;
	}

	.agreement-box {
		position: fixed;
		bottom: 10%;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;

		.ag-box {
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		image {
			width: 24rpx;
			height: 24rpx;
		}

		.agreement {
			font-size: 28rpx;
			font-weight: 500;
			color: #666666;

			.primary-color {
				color: #56B7F5;
			}
		}
	}

	.popu {
		.popu-top {
			padding: 30rpx;
			color: #353B55;
			font-size: 32rpx;
			font-weight: 700;
		}

		.popu-title {
			padding: 30rpx;
			background: #fafafa;
			color: #8f8f8f;
			font-size: 28rpx;
		}

		.popu-main {
			.popu-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx;
				border-bottom: 1rpx solid #D7D9DB;
				color: #353B55;
				font-size: 32rpx;
				font-weight: 700;

				.avatar {
					background: #D7D9DB;

					width: 80rpx;
					height: 80rpx;
					border-radius: 80rpx;

				}
			}
		}

		.popu-footer {
			width: 690rpx;
			height: 88rpx;
			background: $c-bgColor;
			border: none;
			border-radius: 20rpx;
			line-height: 88rpx;
			text-align: center;
			color: #FFFFFF;
			font-weight: 700;
			font-size: 32rpx;
			margin: 40rpx auto;

		}
	}
</style>