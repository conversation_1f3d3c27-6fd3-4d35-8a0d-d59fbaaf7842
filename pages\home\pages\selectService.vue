<template>
	<view class="page">
		<c-navBar title="选择" isTran isPerch :background="ceiling ? 'rgb(253, 220, 232)' : ''"></c-navBar>

		<view class="content">
			<view class="service-list">
				<view class="service-item" v-for="(service, index) in serviceList" :key="index" @click="selectService(service, index)">
					<view class="service-info">
						<view class="service-name">{{ service.name }}</view>
						<view class="service-price">￥{{ service.price }}</view>
						<view class="service-desc">{{ service.introduction }}</view>
					</view>
					<view class="select-icon select flex flex-center" v-if="selectedIndex === index">
						<u-icon name="checkmark" size="32rpx" color="#fff"></u-icon>
					</view>
					<view class="select-icon flex flex-center" v-else>
						<u-icon name="checkmark" size="32rpx" color="#fff"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部确定按钮 -->
		<view class="bottom-bar">
			<button class="order-btn" @click="confirmSelect">确定</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			ceiling: false,
			selectedIndex: 0, // 默认选中第一个
			selectedService: null,
			serviceList: []
		}
	},
	onLoad(options) {
		// 设置默认选中第一个服务
		this.myCity = uni.getStorageSync('city')
		console.log(this.myCity, 'myCity');

		this.getFeedingOrder(this.myCity)
	},
	methods: {
		selectService(service, index) {
			this.selectedIndex = index;
			this.selectedService = service;
		},
		async getFeedingOrder(city) {
			const res = await this.$api.getFeedingOrder({ city })
			console.log(res, 'res');
			this.serviceList = res.data
			this.selectedService = this.serviceList[0];

		},
		confirmSelect() {
			if (this.selectedService) {
				// 将选中的服务信息存储到本地存储
				uni.setStorageSync('selectedService', this.selectedService);
				// 返回上一页
				uni.navigateBack();
			} else {
				uni.showToast({
					icon: 'none',
					title: '请选择一个服务'
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: #F7F3F6;
	padding-bottom: 120rpx;
}

.content {
	padding: 24rpx;
}

.service-list {
	.service-item {
		background: #ffffff;
		border-radius: 24rpx;
		padding: 32rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.service-info {
			flex: 1;

			.service-name {
				font-size: 32rpx;
				font-weight: 600;
				color: #18181A;
				margin-bottom: 16rpx;
			}

			.service-price {
				font-size: 32rpx;
				font-weight: 600;
				color: #FF80B5;
				margin-bottom: 12rpx;
			}

			.service-desc {
				font-size: 28rpx;
				color: #606066;
			}
		}

		.select-icon {
			background: #D0D0D0;
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
		}

		.select {
			background: #FF80B5;
		}
	}
}

.bottom-bar {
	padding: 24rpx;


	.order-btn {
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		left: 24rpx;
		right: 24rpx;
		box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(255, 128, 181, 0.7);
		height: 88rpx;
		background: #FF80B5;
		border-radius: 16rpx;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 600;
		border: none;

		&::after {
			border: none;
		}
	}
}
</style>
