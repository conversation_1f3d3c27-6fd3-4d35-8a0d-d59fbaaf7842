<template>
    <view class="page">
        <c-navBar title="宠物管理" isTran isPerch background="#fff"></c-navBar>

        <view class="content">
            <view class="pet-list" v-if="petList.length > 0">
                <view class="pet-item flex" v-for="(pet, index) in petList" :key="pet.id">
                    <view class="flex align-center flex-1 gap-16">
                        <view class="pet-avatar flex align-center justify-center">
                            <view class="avatar-circle">
                                <text class="avatar-text">{{ pet.petName ? pet.petName.charAt(0) : '宠' }}</text>
                            </view>
                        </view>
                        <view class="pet-info">
                            <view class="pet-name">{{ pet.petName }} {{ getPetTypeText(pet.petType) }}</view>
                            <view class="pet-details">
                                <text class="detail-item">体重：{{ pet.petWeight ? pet.petWeight + 'kg' : '未设置' }}</text>
                                <text class="detail-item">偏好：{{ pet.preference || '无' }}</text>
                            </view>
                        </view>
                    </view>
                    <view class="flex align-start gap-16">
                        <view class="action-btn edit-btn" @click="editPet(pet, index)">
                            <u-icon name="edit-pen" size="24" color="#000"></u-icon>
                        </view>
                        <view class="action-btn delete-btn" @click="deletePet(index)">
                            <u-icon name="trash" size="24" color="#000"></u-icon>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 加载状态 -->
            <view class="loading-state" v-if="loading">
                <text class="loading-text">加载中...</text>
            </view>

            <!-- 空状态 -->
            <view class="empty-state" v-else-if="petList.length === 0">
                <view class="empty-icon">🐾</view>
                <text class="empty-text">还没有添加宠物哦</text>
                <text class="empty-tips">点击下方按钮添加您的爱宠</text>
            </view>
        </view>

        <!-- 底部添加按钮 -->
        <view class="bottom-bar">
            <button class="confirm-btn" @click="addPet">添加宠物</button>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            ceiling: false,
            petList: [],
            loading: false,
        };
    },
    onLoad() {
        this.loadPetList();
    },
    onShow() {
        // 页面显示时重新加载宠物列表
        this.loadPetList();
    },
    methods: {
        // 加载宠物列表
        async loadPetList() {
            this.loading = true;
            try {
                const res = await this.$api.getPetList();
                console.log(res, '宠物列表');
                this.petList = res.data || [];
            } catch (error) {
                console.error('获取宠物列表失败:', error);
                uni.showToast({
                    icon: 'none',
                    title: '获取宠物列表失败'
                });
                this.petList = [];
            } finally {
                this.loading = false;
            }
        },

        // 宠物类型转换
        getPetTypeText(petType) {
            const typeMap = {
                0: '(猫)',
                1: '(狗)',
                '0': '(猫)',
                '1': '(狗)'
            };
            return typeMap[petType] || '';
        },

        // 添加宠物
        addPet() {
            this.$fn.jumpPage("/pages/my/pages/addPet");
        },

        // 编辑宠物
        editPet(pet, index) {
            this.$fn.jumpPage(`/pages/my/pages/addPet?petData=${JSON.stringify(pet)}&index=${index}`);
        },

        // 删除宠物
        deletePet(index) {
            const pet = this.petList[index];
            uni.showModal({
                title: "确认删除",
                content: `确定要删除宠物"${pet.petName}"吗？`,
                success: async (res) => {
                    if (res.confirm) {
                        try {
                            // TODO: 这里应该调用删除API，现在暂时使用本地删除
                            await this.$api.deletePet({ id: pet.id });

                            this.petList.splice(index, 1);
                            uni.showToast({
                                title: "删除成功",
                                icon: "success",
                            });
                        } catch (error) {
                            console.error('删除宠物失败:', error);
                            uni.showToast({
                                icon: 'none',
                                title: '删除失败，请重试'
                            });
                        }
                    }
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    background: linear-gradient(180deg, #ffe8f4 0%, #f7f3f6 50%, #f7f3f6 100%);
    padding-bottom: 120rpx;
}

.content {
    padding: 24rpx;
}

.pet-list {
    .pet-item {
        background: #ffffff;
        border-radius: 24rpx;
        padding: 32rpx;
        margin-bottom: 20rpx;
        gap: 24rpx;

        .pet-avatar {
            width: 120rpx;
            height: 120rpx;

            .avatar-circle {
                width: 120rpx;
                height: 120rpx;
                border-radius: 50%;
                background: linear-gradient(135deg, #ff80b5 0%, #ffb5d1 100%);
                display: flex;
                align-items: center;
                justify-content: center;

                .avatar-text {
                    font-size: 48rpx;
                    font-weight: 600;
                    color: #ffffff;
                }
            }
        }

        .pet-info {
            flex: 1;

            .pet-name {
                font-size: 32rpx;
                font-weight: 600;
                color: #18181a;
                margin-bottom: 16rpx;
            }

            .pet-details {
                display: flex;
                flex-direction: column;
                gap: 8rpx;

                .detail-item {
                    font-size: 28rpx;
                    color: #606066;
                }
            }
        }

        .pet-actions {
            display: flex;
            gap: 16rpx;

            .action-btn {
                width: 48rpx;
                height: 48rpx;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;

                .action-icon {
                    width: 24rpx;
                    height: 24rpx;
                }

                &.edit-btn {
                    background: #e3f2fd;

                    .action-icon {
                        filter: hue-rotate(200deg);
                    }
                }

                &.delete-btn {
                    background: #ffebee;

                    .action-icon {
                        filter: hue-rotate(0deg) saturate(2);
                    }
                }
            }
        }
    }
}

.loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 120rpx 0;

    .loading-text {
        font-size: 28rpx;
        color: #999999;
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;

    .empty-icon {
        font-size: 120rpx;
        margin-bottom: 32rpx;
        opacity: 0.6;
    }

    .empty-text {
        font-size: 32rpx;
        color: #999999;
        margin-bottom: 16rpx;
    }

    .empty-tips {
        font-size: 28rpx;
        color: #C0C0C9;
    }
}

.bottom-bar {
    padding: 24rpx;


    .confirm-btn {
        position: fixed;
        bottom: env(safe-area-inset-bottom);
        left: 24rpx;
        right: 24rpx;
        box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(255, 128, 181, 0.7);
        height: 88rpx;
        background: #FF80B5;
        border-radius: 16rpx;
        color: #ffffff;
        font-size: 32rpx;
        font-weight: 600;
        border: none;

        &::after {
            border: none;
        }
    }
}
</style>
