<template>
	<view class="page">
		<view class="title">
			流水记录
		</view>
		<view class="list-box">
			<c-scroll-list :api="api" :apiParams="apiParams" @load="load" ref="list">
				<view class="itme" v-for="(item,index) in list" :key="index"
					@click="jumpPage({url:'/pages/my/pages/withdrawDetails?id='+item.id})">
					<view class="item-left">
						<view class="title">
							{{item.recordType}}
						</view>
						<view class="time">
							{{item.createTime}}
						</view>
					</view>
					<view class="item-right">
						<view style="">
							<view class="title">
								<span style="color: #FF8E0A;"
									v-if="item.recordType == '提现'">-{{(item.amount/100).toFixed(2)}}元</span>
								<span style="color: #1F662C;"
									v-if="item.recordType == '分佣'">+{{(item.amount/100).toFixed(2)}}元</span>
								<span style="color: #D00C0C;"
									v-if="item.recordType == '退款'">{{(item.amount/100).toFixed(2)}}元</span>
							</view>
							<view class="item-status" v-if="item.recordType == '提现'">
								{{item.serverStatus==0?'待打款':'已打款'}}
							</view>
						</view>
					</view>
				</view>
			</c-scroll-list>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				api: this.$api.withdrawalList, // 列表接口
				apiParams: {
					pageNum: 1,
					pageSize: 10,
				}, // 请求参数
				list: [{
					title: "提现",
					createTime: "2024-07-05 14:06",
					number: "223"
				}, {
					title: "分佣",
					createTime: "2024-07-05 14:06",
					number: "223"
				}, {
					title: "提现",
					createTime: "2024-07-05 14:06",
					number: "223"
				}], // 商品列表
			};
		},
		onShow() {},
		methods: {
			load(res) {
				if (res != null && res.list.length) {
					this.list = res.list;
				} else {
					this.list = [];
				}
			},
			jumpPage(data) {
				console.log(data, 'ddd')
				uni.navigateTo({
					url: data.url
				});
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f7f7f7;

		.page {
			padding: 30rpx;
		}

		.title {}

		.list-box {
			margin-top: 24rpx;

			.itme {
				background: #FFFFFF;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
				border-radius: 16rpx;
				box-sizing: border-box;
				padding: 24rpx 20rpx;
				height: 140rpx;
				display: flex;
				justify-content: space-between;
				margin-bottom: 20rpx;

				.item-left {
					.title {
						text-align: left;
						font-size: 30rpx;
						color: #222229;
					}

					.time {
						margin-top: 20rpx;
						font-size: 24rpx;
						color: #606060;
						text-align: left;
					}
				}

				.item-right {
					display: flex;
					text-align: left;
					font-weight: bold;
					font-size: 30rpx;

					.item-status {
						color: #9B9B9B;
						font-size: 24rpx;
						text-align: right;
						margin-top: 10px;
					}
				}

			}
		}
	}
</style>