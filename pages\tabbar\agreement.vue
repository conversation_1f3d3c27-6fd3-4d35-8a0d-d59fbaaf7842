<template>
	<view id="wrap">
		<u-parse :content="content"></u-parse>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				titList: ['站位', '用户协议', '隐私协议', '支付协议', '关于我们'],
				type: 0, //(1-用户协议 2-隐私协议 3-支付协议 4-关于我们 5-购买协议)
				content: '',
			}
		},
		methods: {
			getInfo() {
				this.$api.getAgreement({
					id: this.type
				}).then(res => {
					console.log('协议res', res);
					this.content = res.data.content
				})
			}
		},
		onLoad(options) {
			this.type = Number(options.type)
			uni.setNavigationBarTitle({
				title: this.titList[this.type]
			})
			this.getInfo()
		}
	}
</script>

<style lang="scss" scoped>
	#wrap {
		padding: 30rpx;
	}
</style>