<template>
	<view class="page">
		<c-navBar title="宠物管理" isTran isPerch background="#fff'"></c-navBar>
		
		<view class="content">
			<view class="form-card">
				<!-- 宠物名称 -->
				<view class="form-item">
					<view class="form-label">宠物名称</view>
					<view class="form-input">
						<u--input 
							v-model="petForm.petName" 
							placeholder="请输入" 
							border="none" 
							inputAlign="right"
						/>
					</view>
				</view>
				
				<!-- 宠物品种 -->
				<view class="form-item" @click="showBreedPicker = true">
					<view class="form-label">宠物品种</view>
					<view class="form-input">
						<text :class="petForm.petType !== '' ? '' : 'placeholder'">
							{{ getPetTypeText(petForm.petType) || '去选择' }}
						</text>
						<image class="arrow-icon" src="../../../static/tabbar/goNext.png" mode="" />
					</view>
				</view>
				
				<!-- 宠物体重 -->
				<view class="form-item">
					<view class="form-label">宠物体重</view>
					<view class="form-input">
						<u--input 
							v-model="petForm.petWeight" 
							placeholder="体重（kg）" 
							border="none" 
							inputAlign="right"
						/>
					</view>
				</view>
				
				<!-- 宠物备好 -->
				<view class="form-item">
					<view class="form-label">宠物备好</view>
					<view class="form-input">
						<u--input 
							v-model="petForm.preference" 
							placeholder="宠物偏好" 
							border="none" 
							inputAlign="right"
						/>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部确认按钮 -->
		<view class="bottom-bar">
			<button 
				class="confirm-btn" 
				@click="confirmAdd"
				:disabled="loading"
				:class="{ 'loading': loading }"
			>
				{{ loading ? '保存中...' : (isEdit ? '确认修改' : '确认添加') }}
			</button>
		</view>

		<!-- 品种选择器 -->
		<u-picker
			:show="showBreedPicker"
			:columns="breedColumns"
			@confirm="onBreedConfirm"
			@cancel="showBreedPicker = false"
		></u-picker>
	</view>
</template>

<script>
export default {
	data() {
		return {
			ceiling: false,
			showBreedPicker: false,
			isEdit: false,
			editIndex: -1,
			loading: false, // 加载状态
			petForm: {
				petName: '',
				petType: '', // 0-猫, 1-狗
				petWeight: '',
				preference: ''
			},
			breedColumns: [
				[
					{ text: '猫', value: 0 },
					{ text: '狗', value: 1 }
				]
			]
		}
	},
	onLoad(options) {
		if (options.petData) {
			// 编辑模式
			this.isEdit = true;
			this.editIndex = parseInt(options.index || 0);
			const petData = JSON.parse(options.petData);
			
			// 映射数据结构
			this.petForm = {
				id: petData.id,
				petName: petData.petName || petData.name || '',
				petType: petData.petType !== undefined ? petData.petType : '',
				petWeight: petData.petWeight || petData.weight || '',
				preference: petData.preference || petData.note || ''
			};
			

		}
	},
	methods: {
		// 品种选择确认
		onBreedConfirm(e) {
			this.petForm.petType = e.value[0].value;
			this.showBreedPicker = false;
		},
		
		// 获取宠物类型文本
		getPetTypeText(petType) {
			const typeMap = {
				0: '猫',
				1: '狗',
				'0': '猫',
				'1': '狗'
			};
			return typeMap[petType] || '';
		},
		
		// 获取用户ID
		getUserId() {
			const user = uni.getStorageSync('user');
			return user ? user.id : null;
		},
		
		// 确认添加/编辑
		async confirmAdd() {
			// 防止重复提交
			if (this.loading) {
				return;
			}
			
			// 表单验证
			if (!this.petForm.petName.trim()) {
				return uni.showToast({
					icon: 'none',
					title: '请输入宠物名称'
				});
			}
			
			if (this.petForm.petType === '') {
				return uni.showToast({
					icon: 'none',
					title: '请选择宠物品种'
				});
			}
			
			// 获取用户ID
			const userId = this.getUserId();
			if (!userId) {
				return uni.showToast({
					icon: 'none',
					title: '请先登录'
				});
			}
			
			this.loading = true;
			
			try {
				// 构造API参数
				const params = {
					petName: this.petForm.petName,
					petType: this.petForm.petType,
					petWeight: this.petForm.petWeight || '',
					preference: this.petForm.preference || '',
					userId: userId
				};
				
				// 如果是编辑模式，添加宠物ID
				if (this.isEdit && this.petForm.id) {
					params.id = this.petForm.id;
				}
				
				console.log('提交宠物信息:', params);
				
				// 调用API
				const res = await this.$api.addAndEditPet(params);
				console.log('API响应:', res);
				
				uni.showToast({
					title: this.isEdit ? '修改成功' : '添加成功',
					icon: 'success'
				});
				
				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				
			} catch (error) {
				console.error('保存宠物信息失败:', error);
				
				// 根据错误类型显示不同的提示信息
				let errorMessage = '保存失败，请重试';
				if (error.message) {
					errorMessage = error.message;
				} else if (error.msg) {
					errorMessage = error.msg;
				}
				
				uni.showToast({
					icon: 'none',
					title: errorMessage
				});
			} finally {
				this.loading = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(180deg, #FFE8F4 0%, #F7F3F6 50%, #F7F3F6 100%);
	padding-bottom: 120rpx;
}

.content {
	padding: 24rpx;
}

.form-card {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 0;
	overflow: hidden;
}

.form-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #F5F5F5;
	
	&:last-child {
		border-bottom: none;
	}
	
	.form-label {
		font-size: 32rpx;
		color: #18181A;
		font-weight: 500;
		min-width: 160rpx;
	}
	
	.form-input {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		gap: 16rpx;
		
		text {
			font-size: 28rpx;
			color: #18181A;
			
			&.placeholder {
				color: #999999;
			}
		}
		
		.arrow-icon {
			width: 12rpx;
			height: 20rpx;
		}
	}
}

.bottom-bar {
	padding: 24rpx;


	.confirm-btn {
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		left: 24rpx;
		right: 24rpx;
		box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(255, 128, 181, 0.7);
		height: 88rpx;
		background: #FF80B5;
		border-radius: 16rpx;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 600;
		border: none;
		transition: all 0.3s ease;

		&.loading, &:disabled {
			background: #D0D0D0;
			color: #999999;
			box-shadow: none;
		}

		&::after {
			border: none;
		}
	}
}
</style>
