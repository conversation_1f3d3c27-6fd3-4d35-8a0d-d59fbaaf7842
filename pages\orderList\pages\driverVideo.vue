<template>
	<view class="page">
		<view class="flip-container" :style="'height: calc(100vh - 520rpx - '+(statusHeight+titleHeight)+'px);'">
			<live-player :src="info.monitorUrl" autoplay mode="live" @statechange="statechange" @error="error"
				class="flipped-video" />
		</view>

		<view class="main">
			<view class="driver">
				<view class="driver_info">
					<image class="avatar" :src="info.avatar?info.avatar:'/static/tabbar/hander.png'" mode=""></image>
					<view class="info">
						<view class="name">{{info.name}}</view>
						<view class="phone">{{info.phone}}</view>
					</view>
				</view>

				<view class="features">
					<view class="features_item" @click="callPhone">
						<image class="features_icon" src="../static/cell.png" mode=""></image>
					</view>
				</view>
			</view>

			<view class="address">
				<image class="address_icon" src="../static/navigation.png" mode=""></image>
				<view class="address_info">
					<view class="name">{{desc?desc:'暂无位置信息'}}</view>
					<view class="detail" v-if="address">{{address}}</view>
				</view>
			</view>
			<view class="footer">
				<button class="pinkbtn" @click="lookPosition">查看位置</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusHeight: 0,
				titleHeight: 0,
				src: '',
				orderId: "",
				desc: '',
				address: '',
				info: {
					avatar: "",
					carNumber: "",
					deviceId: "",
					driverId: '',
					monitorUrl: "",
					name: "",
					orderId: '',
					orderStatus: 0,
					phone: ""
				}
			};
		},
		onLoad(option) {
			this.orderId = option.orderId
			this.getOrderLiveFn(this.orderId)
			setInterval(() => {
				this.$api.liveStop({
					deviceId: this.info.deviceId
				}).then(res => {
					console.log('3分钟后停止');
				})
				this.getOrderLiveFn(this.orderId)
			}, 1000 * 60 * 3)
		},
		methods: {
			getOrderLiveFn(orderId) {
				this.$api.getOrderLive({
					orderId
				}).then(res => {
					console.log(res, 'res');
					this.info = res.data
					if (res.data.carStatus == 2) {
						uni.showToast({
							icon: "none",
							title: '司机暂未启动车辆,请稍后重试!'
						})
					}
					let location = [res.data.locationVO.longitude, res.data.locationVO.latitude].join(',')
					this.$amapFun.getRegeo({
						location,
						success: (data) => {
							//成功回调
							console.log(data, '我的位置信息');
							this.desc = data[0].desc
							this.address = data[0].name
						},
						fail: (info) => {
							//失败回调
							console.log(info)
							if (info.errCode == '10044') {
								uni.showToast({
									icon: 'none',
									title: '当日查询超过限制'
								})
							}
						}
					})
				})
			},

			// 打开定位
			openAddress(v) {
				uni.openLocation({
					address: this.name,
					name: this.desc,
					latitude: Number(v.latitude),
					longitude: Number(v.longitude)
				})
			},

			//查看位置
			lookPosition() {
				uni.redirectTo({
					url: "/pages/orderList/pages/driverPosition?orderId="+this.orderId
				})
			},

			// 拨打电话
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},

			statechange(e) {
				console.log('live-player code:', e.detail.code)
			},
			error(e) {
				console.error('live-player error:', e.detail.errMsg)
			}

		}
	};
</script>

<style lang="scss" scoped>
	.main {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 750rpx;
		height: 520rpx;
		background: #fff;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
	}


	.driver {
		padding: 32rpx;
		box-sizing: border-box;
		margin-bottom: 10rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;

		.driver_info {
			display: flex;
			align-items: center;
			gap: 24rpx;

			.avatar {
				width: 112rpx;
				height: 112rpx;
				border-radius: 80rpx 80rpx 80rpx 80rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				gap: 22rpx;

				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #18181A;
					line-height: 38rpx;
				}

				.phone {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #606066;
					line-height: 28rpx;
				}

			}

		}

		.features {
			display: flex;
			align-items: center;
			gap: 40rpx;

			.features_item {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 12rpx;

				.features_icon {
					width: 36rpx;
					height: 36rpx;
				}
			}

		}
	}

	.address {
		margin: 0 32rpx;
		background: #F7F7FA;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		display: flex;
		align-items: center;
		gap: 20rpx;
		padding: 24rpx 16rpx;
		box-sizing: border-box;

		.address_icon {
			width: 48rpx;
			height: 48rpx;
		}

		.address_info {
			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #18181A;
				line-height: 33rpx;
			}

			.detail {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #606066;
				line-height: 28rpx;
				margin-top: 10rpx;
			}

		}
	}

	/* 新增样式 */
	.flip-container {
		position: relative;
		width: 100%;
		overflow: hidden;
	}

	.flipped-video {
		// transform: scaleY(-1);
		/* 上下翻转关键代码 */
		width: 100%;
		height: 100%;
	}

	.footer {
		width: 686rpx;
		height: 96rpx;
		margin: 36rpx auto;
	}
</style>