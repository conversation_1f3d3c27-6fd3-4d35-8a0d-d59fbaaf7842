<template>
	<view class="page">
		<c-navBar title="选择优惠券" isPerch></c-navBar>
		<couponItem v-for="item in couponList" :coupon="item" :key="item.id" @immediateUse="immediateUse"></couponItem>
		<view class="noData">
			<image class="noContent" src="../../../static/common/noContent.png" mode=""></image>
			暂无数据~
		</view>
		<view class="rebase"></view>
	</view>
</template>

<script>
	import couponItem from '../components/couponItem.vue'
	export default {
		components: {
			couponItem
		},
		data() {
			return {
				couponList: []
			};
		},
		onLoad(option) {
			console.log(option, 'option');
			let {
				city,
				orderPrice
			} = option
			if (city && orderPrice) {
				this.getOrderCouponListFn({
					city,
					orderPrice
				})
			}
		},
		onShow() {},
		methods: {
			getOrderCouponListFn(v) {
				console.log(v, '参数');
				this.$api.getOrderCouponList(v).then(res => {
					console.log(res, '优惠券列表');
					this.couponList = res.data
				})
			},
			// 立即使用
			immediateUse(v) {
				uni.setStorageSync('coupon', v)
				uni.navigateBack()
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		background: #F4F5F7;
		min-height: 100vh;
	}

	.rebase {
		height: 40rpx;
	}
	.noData{
		margin: 300rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		color: #BCBCBC;
		font-size: 28rpx;
		.noContent{
			width: 300rpx;
			height: 140rpx;
		}
	}
</style>