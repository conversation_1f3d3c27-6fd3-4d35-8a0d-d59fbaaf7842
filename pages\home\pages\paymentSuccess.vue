<template>
	<view class="page">
		<c-navBar title="支付成功" isPerch isBack></c-navBar>
		<view class="main">
			<image class="icon" src="../static/paySuccessIcon.png" mode="" />
			<view class="text">支付成功</view>
			<view class="price">-￥{{price?(price/100).toFixed(2):0.00}}</view>
			<view class="back" @click="goHome"><button class="pinkbtn">返回首页</button></view>
			<view class="lookOrder" @click="lookOrder">查看订单</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "paymentSuccess",
		data() {
			return {
				price: 0
			};
		},
		onLoad(option) {
			if (option.price) {
				this.price = Number(option.price)
			}
			uni.setStorageSync('refresh', true)
			uni.setStorageSync('refreshHome', true)
		},
		onShow() {},
		methods: {
			// 查看订单
			lookOrder() {
				uni.switchTab({
					url: '/pages/tabbar/orderList'
				})
			},
			goHome() {
				uni.reLaunch({
					url: '/pages/tabbar/home'
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.main {
		margin-top: 10vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 50rpx;

		.icon {
			width: 240rpx;
			height: 240rpx;
		}

		.text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #18181A;
			line-height: 36rpx;
		}

		.price {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #909099;
			line-height: 28rpx;
		}

		.back {
			width: 360rpx;
			height: 88rpx;
		}

		.lookOrder {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #606066;
			line-height: 28rpx;
		}
	}
</style>