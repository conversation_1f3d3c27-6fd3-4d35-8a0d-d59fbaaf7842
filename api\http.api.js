// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作，更多内容详见uView对拦截器的介绍部分：
// https://uviewui.com/js/http.html#%E4%BD%95%E8%B0%93%E8%AF%B7%E6%B1%82%E6%8B%A6%E6%88%AA%EF%BC%9F

// 引入分工api.js 
import dApi from "./divide/d-api.js"
import qApi from "./divide/q-api.js"


const http = uni.$u.http
// 此处使用了传入的params参数，一切自定义即可
const getApi = (data = {}, config) => http.get('/frontend/login/wechat', {
	params: data,
	...config
});
const postApi = (params = {}, config) => http.post('/frontend/yard/redeemRecord', params, config);
const getArea = (data = '', config) => http.get('/ruoyi/city/getByPid', {
	params: data,
	...config
});
const getAgreement = (data = '', config) => http.get('/system/agreement/getById', {
	params: data,
	...config
});
/**
 * 框架参数接口
 */
const getByKey = (data = {}, config) => http.get('/system/config/getByKey', {
	params: data,
	...config
});

/**
 * 判断是不是第一次登录
 */
const loginWeixin = (params = {}, config) => http.post('/frontend/user/login/first', params, config);

/**
 * 微信授权登录
 */
const loginWechat = (params = {}, config) => http.post('/frontend/user/login', params, config);

/**
 * 新增编辑用户地址
 */
const editAddress = (params = {}, config) => http.post('/frontend/address/edit', params, config);
/**
 * 地址列表
 */
const getAddressList = (data = {}, config) => http.get('/frontend/address/list', {
	params: data,
	...config
});

/**
 * 删除用户地址
 */
const delAddress = (data = {}, config) => http.get('/frontend/address/delete', {
	params: data,
	...config
});
/**
 * 获取当前城市可下单的时间信息
 */
const getCityOrderTime = (data = {}, config) => http.get('/frontend/address/city/order/time', {
	params: data,
	...config
});
/**
 * 获取时间折扣信息
 */
const timeSale = (data = {}, config) => http.get('/backend/sale/time/config', {
	params: data,
	...config
});
/**
 * 获取当前城市当前日期下单的梯度时间状态
 */
const getOrderTimeStatus = (params = {}, config) => http.post('/frontend/address/city/order/time/status', params,
	config);
/**
 * 订单确认页信息
 */
const confirmOrderInfo = (params = {}, config) => http.post('/frontend/order/confirm/info', params, config);
/**
 * 个人中心用户信息
 */
const getUserInfo = (data = {}, config) => http.get('/frontend/user/info', {
	params: data,
	...config
});
/**
 * 修改用户信息
 */
const updateUser = (params = {}, config) => http.post('/frontend/user/update', params, config);
/**
 * 下单查看可使用的优惠券
 */
const getOrderCouponList = (params = {}, config) => http.post('/frontend/coupon/order/list', params, config);
/**
 * 领券中心-优惠券列表
 */
const getCouponList = (params = {}, config) => http.post("/frontend/coupon/list", params, config);

/**
 * 我的优惠券列表
 */
const getMyCouponList = (params = {}, config) => http.post('/frontend/coupon/my/list', params, config);

/**
 * 领取优惠券
 */
const receiveCoupon = (data = {}, config) => http.get('/frontend/coupon/receive', {
	params: data,
	...config
});
/**
 * 立即下单-余额支付
 */
const balancePayment = (params = {}, config) => http.post('/frontend/order/confirm/balance/submit', params, config);
/**
 * 立即下单-微信支付
 */
const wechatPayment = (params = {}, config) => http.post('/frontend/order/confirm/wechat/submit', params, config);
/**
 * 微信支付回调
 */
const wechatCallback = (params = {}, config) => http.post('/frontend/order/wechat/callback', params, config);
/**
 * 订单列表
 */
const getOrderList = (params = {}, config) => http.post('/frontend/order/list', params, config);

/**
 * 订单详情(根据订单编号查询)
 */
const getOrderDetail = (data = {}, config) => http.get('/frontend/order/detail', {
	params: data,
	...config
});
/**
 * 取消订单-传订单id
 */
const orderCancel = (data = {}, config) => http.get('/frontend/order/cancel', {
	params: data,
	...config
});

/**
 * 充值套餐列表
 */
const getRechargeList = (data = {}, config) => http.get('/frontend/recharge/list', {
	params: data,
	...config
});
/**
 * 余额充值-微信支付
 */
const rechargePay = (data = {}, config) => http.get('/frontend/recharge/wechat/pay', {
	params: data,
	...config
});

/**
 * 充值记录/流水记录
 */
const getRechargeRecord = (params = {}, config) => http.post('/frontend/recharge/record', params, config);

/**
 * 获取车辆实时定位
 */
const getOrderCarPosition = (params = {}, config) => http.post('/frontend/order/car/position', params, config);

const getOrderLive = (data = {}, config) => http.get('/frontend/order/car/live', {
	params: data,
	...config
});

// 播放3min后停止车载监控推流
const liveStop = (data = {}, config) => http.get('/frontend/order/car/live/stop', {
	params: data,
	...config
});

const getInviteConfig = (data = {}, config) => http.get('/backend/invite/config', {
	params: data,
	...config
});

const getInviteCommissionDetail = (data = {}, config) => http.get('/frontend/invite/commission/detail', {
	params: data,
	...config
});
const getFeedingOrder = (data = {}, config) => http.get('/frontend/order/feed/project/list', { params: data, ...config });
const getPetList = (data = {}, config) => http.get('/frontend/user/pet/list', { params: data, ...config });
const addAndEditPet = (params = {}, config) => http.post('/frontend/user/pet/add', params, config);
const deletePet = (data = {}, config) => http.get('/frontend/user/pet/del', { params: data, ...config });
const commissionWithdraw = (params = {}, config) => http.post('/frontend/invite/commission/withdraw/apply', params, config);
const withdrawalList = (params = {}, config) => http.post('/frontend/invite/commission/withdraw/list', params, config);
let apiList = {
	...dApi,
	...qApi,
	getByKey, //默认参数
	getArea, //获取城市接口
	getAgreement, //获取协议
	getApi, //get接口示例
	postApi, //post接口示例
	loginWeixin, //判断是不是第一次登录
	loginWechat, //微信授权登录
	editAddress, //新增编辑用户地址
	getAddressList, //地址列表
	delAddress, //删除用户地址
	getCityOrderTime, //获取当前城市可下单的时间信息
	getOrderTimeStatus, //获取当前城市当前日期下单的梯度时间状态
	timeSale, //订单确认页信息
	confirmOrderInfo, //订单确认页信息
	getUserInfo, //个人中心用户信息
	updateUser, //修改用户信息
	getOrderCouponList, //下单查看可使用的优惠券
	getCouponList, //领券中心-优惠券列表
	getMyCouponList, //我的优惠券列表
	receiveCoupon, //领取优惠券
	balancePayment, //立即下单-余额支付
	wechatPayment, //立即下单-微信支付
	getOrderList, //订单列表
	getOrderDetail, //订单详情(根据订单编号查询)
	orderCancel, //取消订单-传订单id
	getRechargeList, //充值套餐列表
	rechargePay, //余额充值-微信支付
	getRechargeRecord, //充值记录/流水记录
	getOrderCarPosition, //获取车辆实时定位
	getOrderLive, //获取车辆监控视频
	liveStop,//车辆监控视频停止推流
	getInviteConfig,//获取邀请开关配置
	getInviteCommissionDetail,//获取邀请分佣详情页面
	commissionWithdraw,//邀请分佣提现申请
	withdrawalList,//邀请分佣提现记录
	getFeedingOrder,//获取喂养套餐列表
	getPetList,//获取宠物列表
	addAndEditPet,//新增编辑宠物
	deletePet,//删除宠物
}
export default {
	...apiList
}