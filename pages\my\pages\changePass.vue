<template>
	<!-- 修改密码 -->
	<view class="change-pass">
		<view class="pd20">
			<view class="ch-title"></view>
			<view class="df jcsb box aic">
				<text class="fs30">原密码</text>
				<view class="ce df aic">
					<input type="text" v-model="passwordInfo.oldPassword" placeholder="请输入">
				</view>
			</view>
			<view class="df jcsb box aic">
				<text class="fs30">新密码</text>
				<view class="ce df aic">
					<input type="text" v-model="passwordInfo.newPassword" placeholder="请输入">
				</view>
			</view>
			<view class="df jcsb box aic">
				<text class="fs30">再次输入</text>
				<view class="ce df aic">
					<input type="text" v-model="passwordInfo.newPasswordSecond" @blur="verify" placeholder="请输入">
				</view>
			</view>

			<view class="btn-box">
				<button class="btn" @click="modifyInfo">确 定</button>
			</view>
		</view>

	</view>
</template>
<script>
	export default {
		data() {
			return {
				passwordInfo: {
					"newPassword": "",
					"newPasswordSecond": "",
					"oldPassword": ""
				},
			}
		},
		created() {},
		methods: {
			// 验证新密码和确认
			verify() {
				if (this.passwordInfo.newPassword != this.passwordInfo.newPasswordSecond) {
					uni.showToast({
						icon: 'none',
						title: '新密码和确认密码不一致'
					})
					return
				}
			},
			// 保存按钮
			async modifyInfo() {
				return 
				const {
					data
				} = await this.$u.changePassword(this.passwordInfo);
				console.log(data);
				if (data == true) {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/tabbar/login'
						});
					}, 2000);
				}

			}
		},

	}
</script>

<style lang="scss" scoped>
	.pd20 {
		padding: 0 20rpx;
	}
	.ch-title{
		font-weight: bold;
		font-size: 32rpx;
		color: #1A1A1A;
		font-style: normal;
	}
	.box {
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #F0F1F5;
		border-radius: 10rpx;
		font-size: 26rpx;

		.ce {
			color: #909299;
		}

		.fs30 {
			font-size: 30rpx;
		}

		.icon {
			width: 30rpx;
			height: 30rpx;
		}

		.icon-back {
			width: 20rpx;
			height: 20rpx;
			margin-left: 8rpx;
		}

		.headImg {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
		}

		input {
			text-align: right;
		}

	}

	.up-box {
		display: flex;
		align-items: center;
		height: 80rpx;

		::v-deep .u-upload__button {
			margin: 0;
		}
	}

	.btn-box {
		display: flex;
		align-items: center;
		position: fixed;
		bottom: 0;
		padding: 10rpx 0rpx 68rpx;
		background: #fff;
		z-index: -1;
		left: 30rpx;

		.btn {
			width: 690rpx;
			height: 96rpx;
			background: linear-gradient(157deg, #FFA00C 31%, #FFBA4F 100%);
			border-radius: 20rpx;
			font-size: 36rpx;
			font-weight: bold;
			color: #FFFFFF;
		}
	}
</style>