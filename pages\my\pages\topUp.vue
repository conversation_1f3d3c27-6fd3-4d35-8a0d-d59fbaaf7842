<template>
	<view class="page">
		<view class="cardbox">
			<view class="">
				<view class="title">当前余额</view>
				<view class="num">{{userInfo.balance?(userInfo.balance/100).toFixed(2):'0.00'}}</view>
			</view>
			<view class="topup" @click="$fn.jumpPage('/pages/my/pages/rechargeRecord')">
				充值记录
			</view>
		</view>

		<view class="main">
			<view class="title">
				<view class="headline"></view>充值套餐
			</view>

			<view class="allCard">
				<view class="card" :class="{active:item.id===activeCard}" v-for="item in packageList" :key="item.id"
					@click="activeCard=item.id">
					<view class="price">{{item.amount}}</view>
					<view class="sell_price">
						<text>售价:</text><text>￥{{item.price?(item.price/100).toFixed(2):0.00}}</text>
					</view>
				</view>
			</view>

		</view>
		<view style="height: 250rpx;">
			
		</view>


		<view class="footer">
			<view class="agreement-box">
				<view class="ag-box" @click="tabAgree">
					<image v-if="isAgree" src="/static/common/select.png" mode=""></image>
					<image v-else src="/static/common/noSelect.png" mode=""></image>
				</view>
				<view class="agreement">
					<text @click.stop="tabAgree">我已阅读并同意</text>
					<text class="primary-color" @click="$fn.jumpPage(`/pages/tabbar/agreement?type=3`)">《支付协议》</text>
				</view>
			</view>
			<button class="pinkbtn" @click="recharge">充值</button>
		</view>
	</view>
</template>

<script>
	export default {
		name: "topUp",
		data() {
			return {
				isAgree: false,
				userInfo: {},
				packageList: [],
				activeCard: ''
			};
		},
		onLoad() {

			this.getRechargeListFn()
		},
		onShow() {
			this.getUserInfoFn()
		},
		methods: {
			// 获取余额
			getUserInfoFn() {
				this.$api.getUserInfo().then(res => {
					console.log(res, '信息');
					this.userInfo = res.data
				})
			},
			// 获取列表
			getRechargeListFn() {
				this.$api.getRechargeList().then(res => {
					console.log(res.data, '列表');
					this.packageList = res.data
				})
			},


			// 同意/不同意协议
			tabAgree() {
				this.isAgree = !this.isAgree
			},

			// 充值
			async recharge() {
				if (!this.isAgree) {
					return uni.showToast({
						title: '请先查看并同意协议',
						icon: 'none',
						duration: 2000
					});
				}
				if (!this.activeCard) {
					return uni.showToast({
						title: '请选择套餐',
						icon: "none"
					})
				}
				try {
					uni.showLoading({
						mask: true
					})
					const res = await this.$api.rechargePay({
						menuId: this.activeCard
					})
					console.log(res, '参数');
					if (res.code == 200 && res.data) {
						console.log('我进来了');
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: res.data.timeStamp,
							nonceStr: res.data.nonceStr,
							package: res.data.package,
							signType: res.data.signType,
							paySign: res.data.paySign,
							success: (res) => {
								console.log('res', res);
								uni.showToast({
									title: '支付成功'
								})

								let amount = this.packageList[this.packageList.findIndex(v => v.id == this
									.activeCard)].amount
								let payAmount = (this.packageList[this.packageList.findIndex(v => v.id ==
									this.activeCard)].price / 100).toFixed(2)
								this.$fn.jumpPage('/pages/my/pages/rechargeSuccess?price=' + amount +
									'&payAmount=' + payAmount)
								uni.hideLoading()
							},
							fail: (err) => {
								console.log('err', err);
								uni.showToast({
									icon: 'error',
									title: '取消支付',
									duration: 1000
								})
								uni.hideLoading()
							},
						});
					} else {
						uni.showToast({
							icon: 'fail',
							title: res.msg
						})
						uni.hideLoading()
					}

				} catch (e) {
					//TODO handle the exception
					uni.showToast({
						icon: 'error',
						title: '支付失败'
					})
					uni.hideLoading()
				}


			}
		}
	};
</script>

<style lang="scss" scoped>
	.cardbox {
		width: 750rpx;
		height: 240rpx;
		background: #FFCCDF;
		padding: 40rpx 32rpx 82rpx 40rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #303033;
			line-height: 33rpx;
		}

		.num {
			font-family: D-DIN Exp-DINExp, D-DIN Exp-DINExp;
			font-weight: bold;
			font-size: 64rpx;
			color: #18181A;
			line-height: 75rpx;
			margin-top: 16rpx;
		}

		.topup {
			width: 160rpx;
			height: 64rpx;
			background: #FF7BAC;
			box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(255, 59, 114, 0.2);
			border-radius: 32rpx 32rpx 32rpx 32rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
			line-height: 64rpx;
			text-align: center;
		}

	}

	.main {
		width: 750rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		margin-top: -40rpx;
		padding: 40rpx 32rpx;
		box-sizing: border-box;

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1A1A1A;
			line-height: 38rpx;
			display: flex;
			align-items: center;

			.headline {
				width: 12rpx;
				height: 32rpx;
				background: #FF7FB5;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
				margin-right: 12rpx;
			}
		}
	}

	.allCard {
		display: grid;
		grid-template-columns: 330rpx 330rpx;
		/* 三列，每列占1/2宽度 */
		gap: 26rpx;
		/* 网格间距 */
		margin-top: 24rpx;


		.card {
			width: 330rpx;
			height: 200rpx;
			background: #F5F5F7;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: 24rpx;
			color: #606066;

			&.active {
				background: #FF7FB5;
				color: #FFFFFF;
			}

			.price {
				font-family: D-DIN Exp, D-DIN Exp;
				font-weight: 400;
				font-size: 64rpx;
				line-height: 75rpx;
			}

			.sell_price {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				line-height: 33rpx;
			}
		}
	}

	.footer {
		width: 100%;
		padding: 0 30rpx 68rpx;
		box-sizing: border-box;
		position: fixed;
		left: 0;
		bottom: 0rpx;
		background-color: #FFFFFF;



		.agreement-box {
			display: flex;
			align-items: center;
			width: 100%;
			margin-bottom: 24rpx;

			.ag-box {
				width: 50rpx;
				height: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			image {
				width: 24rpx;
				height: 24rpx;
			}

			.agreement {
				font-size: 28rpx;
				font-weight: 500;
				color: #666666;

				.primary-color {
					color: #56B7F5;
				}
			}
		}
	}
</style>