<template>
	<view class="page scroll_box">
		<!-- <view class="headBg top">
			<c-navBar title="订单" isTran isPerch isBack></c-navBar>
			<view class="search">
				<u--input inputAlign="center" v-model="apiParams.number" border="none" :focus="focus" @blur="() => { placeholderShow = true, focus = false }" @change="changeOrderId" />
				<view v-if="placeholderShow && !apiParams.number" class="placeholder" @click="() => { placeholderShow = false, focus = true }">
					<image class="search_icon" src="../../static/tabbar/search-h.png" mode=""></image> 订单编号
				</view>
			</view>

			
		</view> -->
		<section class="top-tabs">
			<view class="top-tab" :class="tab === 0 ? 'active' : ''" @click="handleChangeTab(0)">
				<view class="icon">
					<u-image v-if="tab === 0" :fade="false" width="40rpx" height="40rpx" src="/static/common/travel.png" mode="aspectFill"></u-image>
					<u-image v-if="tab === 1" :fade="false" width="40rpx" height="40rpx" src="/static/common/travel-a.png" mode="aspectFill"></u-image>
				</view>
				<text>出行</text>
			</view>
			<view class="top-tab" :class="tab === 1 ? 'active' : ''" @click="handleChangeTab(1)">
				<view class="icon">
					<u-image v-if="tab === 0" :fade="false" width="40rpx" height="40rpx" src="/static/common/feed.png" mode="aspectFill"></u-image>
					<u-image v-if="tab === 1" :fade="false" width="40rpx" height="40rpx" src="/static/common/feed-a.png" mode="aspectFill"></u-image>
				</view>
				<text>喂养</text>
			</view>
		</section>
		<section class="status-tabs" v-if="tab === 0">
			<u-tabs :list="tabList" lineWidth="30" lineHeight="4" :scrollable="true" lineColor="#FF7BAC" :activeStyle="{ color: '#18181A', fontWeight: 'bold', fontSize: '40rpx', padding: '0 10rpx' }" :inactiveStyle="{ color: '#606066', fontSize: '35rpx', padding: '0 10rpx' }" itemStyle='border-bottom:1rpx solid #DDDDE6;height: 35px;display: flex;align-items: center;justify-content: center; padding:0 10rpx' @click="changeTabs">
			</u-tabs>
		</section>
		<view class="status-tabs">
			<view v-for="(it, i) in tab === 0 ? travelStatuses : feedStatuses" :key="i" class="status-item flex flex-center" :class="statusIndex === i ? 'active' : ''" @click="handleChangeStatus(i)">
				{{ it }}
			</view>
		</view>
		<view class="list main" v-if="tab === 0">
			<c-scroll-list ref="list" @load="load" :api="api" :apiParams="apiParams">
				<view class="order" v-for="(item, index) in tableData" :key="item.id" @click.prevent="goOrderDetail(item.number, item.orderStatus)">
					<view class="title">
						<view class="orderid">{{ item.number }}</view>
						<view class="status status_color1" v-if="item.orderStatus == 1">待接单</view>
						<view class="status status_color1" v-if="item.orderStatus == 2">已接单</view>
						<view class="status status_color2" v-if="item.orderStatus == 3">待司机确认</view>
						<view class="status status_color2" v-if="item.orderStatus == 4">进行中</view>
						<view class="status status_color3" v-if="item.orderStatus == 5">已完成</view>
						<view class="status status_color4" v-if="item.orderStatus == 6">已退款</view>
					</view>

					<c-distribution :distributionData="{ address: item.sendAddress, name: item.sendName, phone: item.sendPhone }" type="1" />
					<c-distribution :distributionData="{ address: item.takeAddress, name: item.takeName, phone: item.takePhone }" type="2" />

					<view class="footer">
						<view class="date">{{ item.createTime }}</view>
						<view class="btns">
							<!-- 根据订单状态显示 -->
							<button class="order_btn" v-if="item.orderStatus == 4" @click.stop="$fn.jumpPage('/pages/orderList/pages/driverPosition?orderId=' + item.id)">查看定位</button>
							<button class="order_btn" v-if="item.orderStatus == 4" @click.stop="$fn.jumpPage('/pages/orderList/pages/driverVideo?orderId=' + item.id)">查看视频</button>
							<button class="order_btn" v-if="item.orderStatus == 3 || item.orderStatus == 4" @click.stop="callPhone(item.driverPhone)">联系司机</button>
							<button class="order_btn" v-if="item.orderStatus == 1" @click.stop="cancelOrder(item.id)">取消订单</button>
						</view>
					</view>

				</view>
			</c-scroll-list>
		</view>
		<view class="list main" v-if="tab === 1">
			<c-scroll-list ref="list" @load="load" :api="api" :apiParams="apiParams">
				<view class="card" v-for="(item, index) in tableData" :key="item.id" @click.prevent="goOrderDetail(item.number, item.orderStatus)">
					<view class="row time-row flex align-center">
						<view class="title flex align-center">
							<text class="label">下单时间：</text>
							<text class="val">{{ item.createTime }}</text>
						</view>
						<view>
							<view class="tags" v-if="statusIndex === 0">已支付</view>
							<view class="tags" v-if="statusIndex === 1">进行中</view>
							<view class="tags" v-if="statusIndex === 2">已完成</view>
							<view class="tags" v-if="statusIndex === 3">已退款</view>
						</view>
					</view>

					<view class="route">
						<view class="addr">
							<view class="addr-row flex align-center">
								<view class="addr-text flex align-center gap-10" style="margin-left: 5rpx">
									<u-icon name="map-fill" size="32rpx" color="#3384FE"></u-icon>
									<view class="u-line-1">{{ item.sendAddress }}</view>
								</view>
							</view>
							<!-- <view class="addr-row flex align-center gap-10" @click.stop="callPhone(item.takePhone)">
								<image class="call flex flex-center" src="/static/common/call.png"></image>
								<text class="mf-font-28" style="color: #ff80b5">联系客户</text>
							</view> -->
						</view>
					</view>

					<view class="row flex align-center" v-if="item.orderTime">
						<text class="label" style="font-weight: 400; font-size: 28rpx; color: #625d5d">上门时间：</text>
						<text class="val" style="font-weight: 600; font-size: 28rpx; color: #000">{{ item.orderTime }}</text>
					</view>

					<view class="btns flex justify-between gap-20">
						<view class="seeDetail" @click.stop="goOrderDetail(item.number, item.orderStatus)">查看详情</view>
						<view class="go-btn" v-if="item.orderStatus === 3" @click.stop="goOrderDetail(item.number, item.orderStatus)">开始服务</view>
						<view class="go-btn" v-if="item.orderStatus === 4" @click.stop="goOrderDetail(item.number, item.orderStatus)">完成服务</view>
					</view>
				</view>
			</c-scroll-list>
		</view>

		<!-- 取消订单提示 -->
		<u-modal :show="cancelOrdershow" title="温馨提示" :content='modalContent' showCancelButton confirmColor="#FF7FB5" @cancel="cancelOrdershow = false" @confirm="cancelOrderConfirm"></u-modal>
	</view>
</template>

<script>
export default {
	data() {
		return {
			placeholderShow: true,
			orderNum: '',
			feedStatuses: ["已支付", "进行中", "已完成", "已退款"],
			tabList: [{
				name: '全部'
			}, {
				name: '待接单'
			}, {
				name: '已接单'
			}, {
				name: '待司机确认'
			}, {
				name: '进行中'
			}, {
				name: '已完成'
			}, {
				name: '已退款'
			}],
			distribution: {
				address: '成都XXXX',
				userName: '张语嫣言',
				phone: '15808088888'
			},
			api: this.$api.getOrderList,
			apiParams: {
				number: '',
				orderStatus: null //订单状态(1待接单、2已接单、3待司机确认、4进行中、5已完成、6已退款)
			},
			tableData: [],
			cancelOrdershow: false,
			modalContent: '确定取消当前订单吗？',
			cancelOrderId: '',
			focus: false,
			tab: 0,
			orderType: 0,
			statusIndex: 0
		}
	},
	onShow() {
		if (uni.getStorageSync('refresh')) {
			setTimeout(() => {
				this.$refs.list.refresh()
			})
			uni.removeStorageSync('refresh')
		}
	},
	methods: {
		handleChangeStatus(i) {
			this.tableData = [];
			this.statusIndex = i;
			this.apiParams.pageNum = 1;
			this.apiParams.orderStatus = i +1;
			this.apiParams.number = this.searchValue;
			this.$refs.list.refresh();
		},
		handleChangeTab(i) {
			this.tab = i;
			this.orderType = i;
		},
		//输入订单号
		changeOrderId() {
			uni.$u.debounce(this.$refs.list.refresh(), 500)
		},

		// 切换状态
		changeTabs(v) {
			this.tableData = []
			console.log(v, '状态');
			if (v.index == 0) {
				this.apiParams.orderStatus = null
			} else {
				this.apiParams.orderStatus = v.index
			}
			this.$refs.list.refresh()
		},

		load(res) {
			console.log(res, '订单');
			this.tableData = res.list
		},

		// 取消订单
		cancelOrder(id) {
			this.cancelOrderId = id
			this.cancelOrdershow = true
		},

		// 确定取消订单
		cancelOrderConfirm() {
			this.cancelOrdershow = false
			this.$api.orderCancel({
				orderId: this.cancelOrderId
			}).then(res => {
				uni.navigateTo({
					url: '/pages/orderList/pages/orderCancelSuccess'
				})
			})
		},


		// 跳转订单详情
		goOrderDetail(number, status) {
			if(this.tab === 0){
				uni.navigateTo({
					url: `/pages/orderList/pages/orderDetail?number=${number}&status=${status}`,
				})
				return
			}	else{
				uni.navigateTo({
					url: `/pages/orderList/pages/orderDetail2?number=${number}&status=${status}`,
				})
				return
			}
	
		},
		// 拨打电话
		callPhone(phone) {
			uni.makePhoneCall({
				phoneNumber: phone,
				success() {
					console.log('拨打成功');
				},
				fail(err) {
					console.log('拨打失败');
				}
			})
		},

	}
}
</script>

<style lang="scss" scoped>
::v-deep.u-tabs__wrapper__nav__line.data-v-48634e29 {
	bottom: 0;
}

.page {
	min-height: 100vh;
	background: #F5F6F7;

	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;

	.top-tabs {
		display: flex;
		background: #fff;
		padding: 24rpx 0 16rpx 0;
		position: relative;
		z-index: 99;

		.top-tab {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 400;
			font-size: 32rpx;
			color: #6b7280;
			position: relative;
			z-index: 99;

			.icon {
				margin-right: 8rpx;
			}

			&.active {
				color: #000000;
				font-weight: 600;
				font-size: 32rpx;

				&::after {
					content: "";
					position: absolute;
					bottom: -16rpx;
					left: 50%;
					transform: translateX(-50%) skewX(-22deg);
					width: 120rpx;
					height: 14rpx;
					background: #ff80b5;
					border-radius: 999rpx;
				}
			}
		}

	}

	.status-tabs {
		display: flex;
		justify-content: space-between;
		padding: 23rpx 32rpx;
		position: relative;
		z-index: 99;
		padding-top: 28rpx;
		background: linear-gradient(to bottom, #fff 0%, #f8f7f7 100%);

		.status-item {
			width: 148rpx;
			height: 64rpx;
			background: #ededeb;
			border-radius: 16rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #000000;
			line-height: 64rpx;
		}

		.status-item.active {
			background: #ff80b5;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #ffffff;
		}
	}

}

.headBg {
	width: 750rpx;
	height: 400rpx;
	background: linear-gradient(360deg, rgba(255, 204, 223, 0) 0%, #FFD9E7 100%);
}

.search {
	width: 702rpx;
	height: 80rpx;
	background: #FFFFFF;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	margin: 10rpx auto;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;

	.placeholder {
		position: absolute;
		top: 0;
		left: 0;
		width: 702rpx;
		height: 80rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #909099;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10rpx;

		.search_icon {
			width: 24rpx;
			height: 24rpx;
		}
	}
}

.list {
	padding: 0 32rpx;
	padding-bottom: 30rpx;

}

.card {
	margin-bottom: 24rpx;
	background: #ffffff;
	border-radius: 16rpx;
	padding: 22rpx;

	.row {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		margin-top: 10rpx;
	}

	.time-row {
		position: relative;

		.title {
			flex: 1;

			.label {
				font-weight: 400;
				font-size: 28rpx;
				color: #625d5d;
			}

			.val {
				font-weight: 600;
				font-size: 28rpx;
				color: #000;
			}
		}

		.tags {
			width: 96rpx;
			height: 48rpx;
			background: #eef6ff;
			border-radius: 8rpx;
			font-size: 24rpx;
			color: #0f6eff;
			line-height: 48rpx;
			text-align: center;
		}
	}

	.route {
		display: flex;
		padding: 8rpx 0 12rpx;
	}

	.route.single {
		padding-bottom: 4rpx;
	}

	.dot {
		width: 8rpx;
		height: 8rpx;
		background: #3ca0ff;
		border-radius: 50%;
		margin: 18rpx 16rpx 0 8rpx;
	}

	.addr {
		flex: 1;
		position: relative;
		background: #f8fafb;
		padding: 0 30rpx;

		.line {
			position: absolute;
			top: 50rpx;
			left: 30rpx;
			height: 72rpx;
			width: 14rpx;
		}
	}

	.addr-row {
		display: flex;
		align-items: center;
		padding: 20rpx 0;

		.point {
			width: 12rpx;
			height: 12rpx;
			border-radius: 50%;
		}
	}

	.call-row {
		font-weight: 400;
		font-size: 28rpx;
		color: #ff80b5;
		line-height: 40rpx;
		margin-bottom: 22rpx;

		image {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.addr-text {
		flex: 1;
		font-weight: 400;
		font-size: 28rpx;
		color: #333333;

		.tag {
			width: 50rpx;
			height: 48rpx;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.call {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		background: #fff0f6;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.link {
		color: #ff80b5;
	}

	.link-text {
		margin-left: 8rpx;
		color: #ff80b5;
	}

	.btns {
		display: flex;
		justify-content: flex-end;
		gap: 16rpx;
		margin-top: 10rpx;

		.seeDetail {
			width: 176rpx;
			height: 72rpx;
			background: #f3f4f6;
			border-radius: 16rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #374151;
			line-height: 72rpx;
			text-align: center;
		}

		.go-btn {
			width: 176rpx;
			height: 72rpx;
			background: rgba(255, 128, 181, 0.2);
			border-radius: 16rpx;
			font-weight: 500;
			font-size: 28rpx;
			color: #ff80b5;
			line-height: 72rpx;
			text-align: center;
		}
	}
}

.order {
	width: 702rpx;
	background: #FFFFFF;
	border-radius: 12rpx 12rpx 12rpx 12rpx;
	margin: 20rpx auto;

	.title {
		height: 76rpx;
		padding: 0rpx 20rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #E6E6E6;

		.orderid {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 24rpx;
			color: #1A1A1A;
			line-height: 28rpx;
		}

		.status {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			line-height: 33rpx;
		}

		.status_color1 {
			color: #FF3B72;
		}

		.status_color2 {
			color: #FF8326;
		}

		.status_color3 {
			color: #56B7F5;
		}

		.status_color4 {
			color: #909099;
		}
	}
}


.footer {
	height: 90rpx;
	padding: 0rpx 20rpx;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-top: 1rpx solid #E6E6E6;

	.date {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #606066;
		line-height: 28rpx;
	}

	.btns {
		display: flex;
		align-items: center;
		gap: 20rpx;

		.order_btn {
			width: 128rpx;
			height: 48rpx;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			border: 1rpx solid #606066;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #18181A;
			line-height: 46rpx;
			text-align: center;
			padding: 0;
		}
	}
}
</style>