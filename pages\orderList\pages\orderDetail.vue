<template>
	<view class="page">
		<view class="head bg1" :class="'bg' + orderDetail.orderStatus">
			<c-navBar title="订单详情" isTran isPerch
				:background="ceiling&&orderDetail.orderStatus=='1'?'rgb(253, 220, 232)'
				:ceiling&&orderDetail.orderStatus=='2'?'rgb(253, 220, 232)'
				:ceiling&&orderDetail.orderStatus=='3'?'rgb(255, 234, 196)'
				:ceiling&&orderDetail.orderStatus=='4'?'rgb(217, 240, 255)'
				:ceiling&&orderDetail.orderStatus=='5'?'rgb(236, 238, 240)':''"></c-navBar>
			<view class="status" v-if="orderDetail.orderStatus=='1'">
				<view class="status_title">待接单</view>
				<view class="explain">订单已提交成功，正在等待工作人员确认...</view>
			</view>
			<view class="status" v-if="orderDetail.orderStatus=='2'">
				<view class="status_title">已接单</view>
				<view class="explain">我们正在快速为您快速匹配最近的司机，请耐心等待...</view>
			</view>
			<view class="status" v-if="orderDetail.orderStatus=='3'">
				<view class="status_title">待司机确认</view>
				<view class="explain">司机会尽快和您联系确认订单，请耐心等待...</view>
			</view>
			<view class="status" v-if="orderDetail.orderStatus=='4'">
				<view class="status_title">进行中</view>
				<view class="explain">司机正在运送中，请耐心等待...</view>
			</view>
			<view class="status" v-if="orderDetail.orderStatus=='5'">
				<view class="status_title">已完成</view>
				<view class="explain">订单已运送完成，如有疑问可联系客服</view>
			</view>
			<view class="status" v-if="orderDetail.orderStatus=='6'">
				<view class="status_title">已退款</view>
				<view class="explain">您已成功退款</view>
			</view>
		</view>
		<view class="radiu"></view>

		<view class="driver" v-if="orderDetail.orderStatus=='3'||orderDetail.orderStatus=='4'">
			<view class="driver_info">
				<image class="avatar" src="../../../static/tabbar/hander.png" mode=""></image>
				<view class="info">
					<view class="name">{{orderDetail.driverInfo?orderDetail.driverInfo.name:'暂无~'}}</view>
					<view class="phone">{{orderDetail.driverInfo?$fn.phoneEn(orderDetail.driverInfo.phone):'暂无~'}}
					</view>
				</view>
			</view>

			<view class="features">
				<view class="features_item" v-if="orderDetail.orderStatus=='4'"
					@click="$fn.jumpPage('/pages/orderList/pages/driverPosition?orderId='+orderDetail.id)">
					<image class="features_icon" src="../static/checkLocation.png" mode=""></image>
					<view class="features_text">查位置</view>
				</view>
				<view class="features_item" v-if="orderDetail.orderStatus=='4'"
					@click="$fn.jumpPage('/pages/orderList/pages/driverVideo?orderId='+orderDetail.id)">
					<image class="features_icon" src="../static/watchVideo.png" mode=""></image>
					<view class="features_text">看视频</view>
				</view>
				<view class="features_item" @click="callPhone">
					<image class="features_icon" src="../static/cell.png" mode=""></image>
					<view class="features_text">打电话</view>
				</view>
			</view>
		</view>

		<view class="main">
			<view class="appointment_time"
				:style="orderDetail.orderStatus=='2'||orderDetail.orderStatus=='3'?'padding-top: 20rpx;':''">
				预约时间：{{orderDetail.orderTime}}</view>
			<c-distribution type="1"
				:distributionData="{address:orderDetail.sendAddress,name:orderDetail.sendName,phone:orderDetail.sendPhone}" />
			<c-distribution type="2"
				:distributionData="{address:orderDetail.takeAddress,name:orderDetail.takeName,phone:orderDetail.takePhone}" />

			<view class="pet_name">
				<view class="pet">爱宠名称</view>
				<view class="name">{{orderDetail.petName}}</view>
			</view>

			<view class="remarks">
				<view class="remarks_title">订单备注</view>
				<view class="remarks_content">
					{{orderDetail.remark}}
				</view>
			</view>
		</view>
		<view class="center">
			<view class="item">
				<view class="item_title">订单编号</view>
				<view class="item_detail">{{orderDetail.number}}</view>
			</view>
			<view class="item">
				<view class="item_title">下单时间</view>
				<view class="item_detail">{{orderDetail.createTime}}</view>
			</view>
			<view class="item">
				<view class="item_title">合计费用</view>
				<view class="item_detail">￥{{orderDetail.totalAmount?(orderDetail.totalAmount/100).toFixed(2):'0.00'}}
				</view>
			</view>
			<view class="item">
				<view class="item_title">优惠券</view>
				<view class="item_detail">
					-￥{{orderDetail.couponAmount?(orderDetail.couponAmount/100).toFixed(2):'0.00'}}</view>
			</view>
			<view class="item">
				<view class="item_title">时间折扣</view>
				<view class="item_detail">
					-￥{{orderDetail.timeDiscountAmount?(orderDetail.timeDiscountAmount/100).toFixed(2):'0.00'}}</view>
			</view>
			<view class="item">
				<view class="item_title">支付方式</view>
				<view class="item_detail">{{orderDetail.payType==1?'微信':'余额'}}支付</view>
			</view>
			<view class="item">
				<view class="item_title">实际支付</view>
				<view class="payment">￥{{orderDetail.payAmount?(orderDetail.payAmount/100).toFixed(2):'0.00'}}</view>
			</view>
		</view>

		<view class="center mt10" v-if="orderDetail.orderStatus==4">
			<view class="item">
				<view class="item_title">退款时间</view>
				<view class="item_detail">{{orderDetail.refundTime}}</view>
			</view>
			<view class="item">
				<view class="item_title">退回方式</view>
				<view class="item_detail">原路返回</view>
			</view>
			<view class="item">
				<view class="item_title">退回金额</view>
				<view class="item_detail">￥{{orderDetail.refundAmount?(orderDetail.refundAmount/100).toFixed(2):'0.00'}}
				</view>
			</view>
		</view>

		<view class="footer" v-if="orderDetail.orderStatus==1">
			<button class="pinkbtn" @click="cancelOrdershow=true">取消订单</button>
		</view>
		<view class="footer" v-if="orderDetail.orderStatus==2||orderDetail.orderStatus==3">
			<button class="pinkbtn" @click="$fn.jumpPage('/pages/my/pages/service')">联系客服</button>
		</view>
		<!-- 取消订单提示 -->
		<u-modal :show="cancelOrdershow" title="温馨提示" :content='modalContent' showCancelButton confirmColor="#FF7FB5"
			@cancel="cancelOrdershow=false" @confirm="cancelOrderConfirm"></u-modal>
	</view>
</template>

<script>
	export default {
		name: "Name",
		data() {
			return {
				cancelOrdershow: false,
				modalContent: '确定取消当前订单吗？',
				orderStatus: '',
				orderDetail: {},
				ceiling: false
			};
		},
		onPageScroll(e) {
			console.log(e, '滚动距离');
			if (e.scrollTop > 0) {
				this.ceiling = true
			} else {
				this.ceiling = false
			}
		},
		onLoad(option) {
			console.log(option, 'option');
			this.orderDetail.orderStatus = option.status
			this.getOrderDetailFn(option.number)
		},
		onShow() {},
		methods: {

			// 查询订单详情
			getOrderDetailFn(number) {
				this.$api.getOrderDetail({
					number
				}).then(res => {
					console.log(res, '订单详情');
					this.orderDetail = res.data
				})
			},



			// 确定取消订单
			cancelOrderConfirm() {
				this.cancelOrdershow = false
				this.$api.orderCancel({
					orderId: this.orderDetail.id
				}).then(res => {
					uni.redirectTo({
						url: '/pages/orderList/pages/orderCancelSuccess'
					})
				})
			},

			// 拨打电话
			callPhone() {
				uni.makePhoneCall({
					phoneNumber: this.orderDetail.driverInfo.phone,
					success() {

					},
					fail() {
						console.log(取消打电话);
					}
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		min-height: 100vh;
		background: #F5F6F7;
	}



	.head {
		width: 750rpx;
		height: 380rpx;


		&.bg1 {
			background: #FFD9E7;
		}

		&.bg2 {
			background: #FFEAC4;
		}

		&.bg3 {
			background: #D9F0FF;
		}

		&.bg4 {
			background: #EBEEF0;
		}

		.status {
			margin: 40rpx 32rpx;

			.status_title {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 36rpx;
				color: #18181A;
				line-height: 42rpx;
			}

			.explain {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 28rpx;
				color: #303033;
				line-height: 33rpx;
				margin-top: 20rpx;
			}
		}
	}

	.radiu {
		width: 750rpx;
		background: #FFFFFF;
		margin-top: -40rpx;
		height: 32rpx;
		border-radius: 32rpx 32rpx 0rpx 0rpx;
	}

	.driver {
		padding: 0 32rpx 32rpx;
		box-sizing: border-box;
		margin-bottom: 10rpx;
		display: flex;
		align-items: flex-end;
		justify-content: space-between;
		background-color: #fff;

		.driver_info {
			display: flex;
			align-items: center;
			gap: 24rpx;

			.avatar {
				width: 112rpx;
				height: 112rpx;
				border-radius: 80rpx 80rpx 80rpx 80rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				gap: 22rpx;

				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #18181A;
					line-height: 38rpx;
				}

				.phone {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #606066;
					line-height: 28rpx;
				}

			}

		}

		.features {
			display: flex;
			align-items: center;
			gap: 40rpx;

			.features_item {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 12rpx;

				.features_icon {
					width: 36rpx;
					height: 36rpx;
				}

				.features_text {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 20rpx;
					color: #18181A;
					line-height: 23rpx;
				}

			}

		}
	}



	.main {
		width: 750rpx;
		background: #FFFFFF;
		padding: 0 32rpx 32rpx;
		box-sizing: border-box;

		.appointment_time {

			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #1A1A1A;
			padding: 0 0 20rpx;
			border-bottom: 1rpx solid #E6E6E6;
		}

		.pet_name {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 0;
			border-top: 1rpx solid #E6E6E6;
			border-bottom: 1rpx solid #E6E6E6;

			.pet {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #18181A;
			}

			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #606066;
			}
		}

		.remarks {

			.remarks_title {
				padding: 24rpx 0;
				line-height: 24rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #18181A;
			}

			.remarks_content {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #606066;
				line-height: 40rpx;
			}
		}

	}

	.mt10 {
		margin: 10rpx 0;
	}

	.center {
		margin: 10rpx 0;
		padding: 0 32rpx;
		box-sizing: border-box;
		background: #fff;

		.item {
			padding: 24rpx 0;
			border-bottom: 1rpx solid #E6E6E6;
			display: flex;
			align-items: center;
			justify-content: space-between;

			&:nth-last-child(1) {
				border: none;
			}

			.item_title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #18181A;
				line-height: 33rpx;
			}

			.item_detail {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #606066;
				line-height: 28rpx;
			}

			.payment {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 24rpx;
				color: #18181A;
				line-height: 28rpx;
			}
		}

	}

	.footer {
		width: 750rpx;
		padding: 40rpx 32rpx;
		box-sizing: border-box;
		background-color: #fff;
	}

	::v-deep.u-modal__content__text.data-v-0156a215 {
		text-align: center;
	}
</style>