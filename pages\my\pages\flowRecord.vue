<template>
	<view class="page">
		<view class="cardbox">
			<view class="">
				<view class="title">当前余额</view>
				<view class="num">{{userInfo.balance?(userInfo.balance/100).toFixed(2):'0.00'}}</view>
			</view>
			<view class="topup" @click="$fn.jumpPage('/pages/my/pages/topUp')">
				充值
			</view>
		</view>

		<view class="main">
			<view class="header">
				<view class="headline"></view>流水记录
			</view>

			<view class="content">
				<c-scroll-list ref="list" @load="load" :option="{offsetBottom:60}" :api="api" :apiParams="apiParams">
					<view class="list" v-for="item in tableData" :key="item.date">
						<view class="date">{{item.date}}</view>
						<view class="record">
							<view class="item" v-for="v in item.list" :key="v.id">
								<view class="top">
									<view class="title">{{v.remark}}</view>
									<view class="text">{{v.createTime?$fn.parseTime(v.createTime):'暂无~'}}</view>
								</view>
								<view class="bottom">
									<view class="price" :class="v.amount>0?'add':'reduce'">
										{{v.amount>0?'+':''}}{{v.amount?(v.amount/100).toFixed(2):'0.00'}}
									</view>
								</view>
							</view>
						</view>
					</view>
				</c-scroll-list>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		name: "topUp",
		data() {
			return {
				api: this.$api.getRechargeRecord,
				apiParams: {
					type: null,
				},
				tableData: [],
				userInfo:{}
			};
		},
		onLoad() {},
		onShow() {
			this.getUserInfoFn()
		},
		methods: {
			// 获取余额
			getUserInfoFn() {
				this.$api.getUserInfo().then(res => {
					console.log(res, '信息');
					this.userInfo = res.data
				})
			},
			
			
			load(res) {
				console.log(res, '流水记录');
				let month = []
				res.list.forEach(v => {
					let date = new Date(v.createTime).getFullYear() + '年' + (new Date(v.createTime).getMonth() +
						1) + '月'
					if (month.length == 0) {
						month.push({
							date,
							list: []
						})
					} else {
						if (month.findIndex(v => v.date == date) == -1) {
							month.push({
								date,
								list: []
							})
						}
					}

					month[month.findIndex(v => v.date == date)].list.push(v)

				})

				this.tableData = month


			},
		}
	};
</script>

<style lang="scss" scoped>
	.page {
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.cardbox {
		width: 750rpx;
		height: 240rpx;
		background: #FFCCDF;
		padding: 40rpx 32rpx 82rpx 40rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #303033;
			line-height: 33rpx;
		}

		.num {
			font-family: D-DIN Exp-DINExp, D-DIN Exp-DINExp;
			font-weight: bold;
			font-size: 64rpx;
			color: #18181A;
			line-height: 75rpx;
			margin-top: 16rpx;
		}

		.topup {
			width: 160rpx;
			height: 64rpx;
			background: #FF7BAC;
			box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(255, 59, 114, 0.2);
			border-radius: 32rpx 32rpx 32rpx 32rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
			line-height: 64rpx;
			text-align: center;
		}

	}

	.main {
		overflow: scroll;
		flex: 1;
		width: 750rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 0rpx 0rpx;
		margin-top: -40rpx;
		padding: 40rpx 0;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;

		.header {
			padding: 0 32rpx 40rpx;
			box-sizing: border-box;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #1A1A1A;
			line-height: 38rpx;
			display: flex;
			align-items: center;

			.headline {
				width: 12rpx;
				height: 32rpx;
				background: #FF7FB5;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
				margin-right: 12rpx;
			}
		}

		.content {
			flex: 1;
			overflow: scroll;
		}

		.list {

			.date {
				width: 750rpx;
				padding: 16rpx 32rpx;
				box-sizing: border-box;
				background: #F8F9FA;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #909099;
				line-height: 33rpx;
			}

			.record {
				width: 750rpx;
				padding: 0 32rpx;
				box-sizing: border-box;

				.item {
					width: 100%;
					padding: 32rpx 0 24rpx;
					box-sizing: border-box;
					border-bottom: 1rpx solid #E6E6E6;
					display: flex;
					align-items: center;
					justify-content: space-between;

					&:nth-last-child(1) {
						border: none;
					}

					.top {


						.title {
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 28rpx;
							color: #18181A;
							line-height: 33rpx;
						}

						.text {
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 24rpx;
							color: #909099;
							line-height: 28rpx;
							margin-top: 12rpx;
						}


					}

					.bottom {

						.price {
							font-family: PingFang SC, PingFang SC;
							font-weight: bold;
							font-size: 32rpx;
							line-height: 32rpx;

							&.add {
								color: #56B7F5;
							}

							&.reduce {
								color: #FF7BAC;
							}
						}
					}


				}
			}
		}
	}
</style>