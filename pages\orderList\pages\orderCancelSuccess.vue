<template>
	<view class="page">
		<c-navBar title="订单取消成功" isPerch></c-navBar>
		<view class="main">
			<image class="icon" src="../static/paySuccessIcon.png" mode="" />
			<view class="text">订单取消成功</view>
			<view class="price">退款将返回原支付账户</view>
			<view class="back" @click="$fn.jumpBack"><button class="pinkbtn">返回</button></view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "paymentSuccess",
		data() {
			return {};
		},
		onLoad() {
			uni.setStorageSync('refresh',true)
		},
		onShow() {},
		methods: {
			// 查看订单
			lookOrder(){
				this.$fn.jumpPage()
			}
		}
	};
</script>

<style lang="scss" scoped>
	
	.main{
		margin-top: 10vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 50rpx;
		
		.icon{
			width: 240rpx;
			height: 240rpx;
		}
		.text{ 
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #18181A;
			line-height: 36rpx;
		}
		.price{
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #909099;
			line-height: 28rpx;
		}
		
		.back{
			width: 360rpx;
			height: 88rpx;
		}
		.lookOrder{
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #606066;
			line-height: 28rpx;
		}
	}

</style>