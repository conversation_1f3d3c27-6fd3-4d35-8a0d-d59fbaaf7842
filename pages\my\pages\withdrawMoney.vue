<template>
	<view class="container">
		<!-- 到账账户 -->
		<view class="account">
			<view class="lable">
				到账账户
			</view>
			<u--form labelPosition="top" :model="form" :rules="rules" ref="uForm1">
				<u-form-item prop="wechatAccount">
					<u--input clearable v-model="form.wechatAccount" border="none" inputAlign="right"
						placeholder="请输入微信号"></u--input>
				</u-form-item>
			</u--form>
		</view>
		<!-- 到账账户 -->
		<!-- 提现金额 -->
		<view class="withdrawalAmount">
			<view class="lable">
				提现金额
			</view>
			<view class="money-box">
				<view class="top-box">
					<view class="input-box">
						<u--form labelPosition="top" :model="form" :rules="rules" ref="uForm2">
							<u-form-item prop="amount">
								<u--input clearable type="digit" height="80" v-model="form.amount" border="none"
									fontSize="80rpx" placeholder="0.00"></u--input>
							</u-form-item>
						</u--form>
					</view>
					<view class="all-btn" @click="clickAll">
						全部提现
					</view>
				</view>
				<view class="bottom-box">
					可提现余额：<span style="font-weight: 700;">{{(balance/100).toFixed(2)}}</span>元
				</view>
				<view class="tips">
					<span style="padding: 0 20rpx 0 10rpx ;">·</span><span>最低只能提现100元</span>
				</view>
			</view>
		</view>
		<!-- 提现金额 -->
		<view class="submit-btn" @click="submit">
			确认提交
		</view>
		<view class="record">
			<view class="text" @click="$fn.jumpPage('/pages/my/pages/withdrawRecords')">
				流水记录
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				balance: "0.00",
				form: {
					wechatAccount: "",
					amount: ""
				},
				rules: {
					'wechatAccount': {
						type: 'string',
						required: true,
						message: '请输入微信号',
						trigger: ['blur', 'change']
					},
					'amount': [{
						required: true,
						message: '请填写金额',
						trigger: ['blur', 'change'],
					}, {
						// 自定义验证函数，见上说明
						validator: (rule, value, callback) => {
							console.log(Number(value) > Number(this.balance));
							if (Number(value) > Number(this.balance)) {
								console.log(value, this.balance)
								callback(new Error('超出可提现余额'));
							} else {
								return
							}
						},
						message: '超出可提现余额',
						// 触发器可以同时用blur和change
						trigger: ['change', 'blur'],
					}, {
						// 自定义验证函数，见上说明
						validator: (rule, value, callback) => {
							console.log(Number(value) > Number(this.balance));
							if (Number(value) <= 0) {
								console.log(value, this.balance)
								callback(new Error('请填写正确金额'));
							} else {
								return
							}
						},
						message: '请填写正确金额',
						// 触发器可以同时用blur和change
						trigger: ['change', 'blur'],
					}],
				}

			};
		},
		onLoad(option) {
			this.balance = option.commissionBalance
		},
		onReady() {
			this.$refs.uForm1.setRules(this.rules)
			this.$refs.uForm2.setRules(this.rules)
		},
		methods: {
			clickAll() {
				this.form.amount = (this.balance / 100).toFixed(2)
				this.$refs.uForm1.validateField('amount')
			},
			submit() {
				uni.$u.throttle(() => {
					let v1 = this.$refs.uForm1.validate()
					let v2 = this.$refs.uForm2.validate()
					Promise.all([v1, v2]).then(res => {
						console.log("this.form=====》", this.form)
						// 调用提交接口
						let parm = {
							wechatAccount: this.form.wechatAccount,
							amount: this.form.amount * 100
						}
						this.$api.commissionWithdraw(parm).then(res => {
							if (res.code == 200) {
								uni.showToast({
									icon: "success",
									title: '提交成功等待打款',
									duration: 1000, // 明确设置持续时间
									success: () => {
										// 在Toast显示完成后跳转
										setTimeout(() => {
											uni.redirectTo({
												url: '/pages/my/pages/withdrawRecords'
											});
										}, 1000);
									}
								});
							}
						});
					})
				}, 3000, true)
			},
		}
	}
</script>

<style lang="scss">
	page {
		background: #F6F6F6;
	}

	.container {
		padding: 20rpx;

		.account {
			height: 120rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-sizing: border-box;
			padding: 0 40rpx;

			.lable {
				font-size: 28rpx;
				color: #333333;
			}
		}

		.withdrawalAmount {
			margin-top: 22rpx;
			height: 360rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			box-sizing: border-box;
			padding: 30rpx 40rpx 0 40rpx;

			.lable {
				font-size: 28rpx;
				color: #333333;
			}

			.money-box {
				margin-top: 35rpx;

				.top-box {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.input-box {
						flex: 1;

						::v-deep .u-input__content__field-wrapper__field {
							height: 80rpx;
						}

					}

					.all-btn {
						margin-left: 40rpx;
						font-weight: 500;
						font-size: 28rpx;
					}

					border-bottom: 1px solid #E6E6E6;
				}

				.bottom-box {
					margin-top: 20rpx;
					font-size: 28rpx;
					color: #333333;
				}

				.tips {
					margin-top: 22rpx;
					font-size: 24rpx;
					color: #9B9B9B
				}
			}

		}

		.submit-btn {
			margin-top: 40rpx;
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			background: #FF7BAC;
			border-radius: 400rpx 400rpx 400rpx 400rpx;
			font-size: 32rpx;
			color: #FFFFFF;
		}

		.record {
			margin-top: 40rpx;
			font-size: 28rpx;
			color: #000000;
			display: flex;
			justify-content: center;

			.text {
				border-bottom: 1rpx solid #000000;
			}
		}
	}
</style>