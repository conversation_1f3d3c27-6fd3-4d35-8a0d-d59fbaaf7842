<template>
	<view class="page">
		<c-navBar title="订单支付" isPerch></c-navBar>

		<view class="info">
			<view class="amount">实付金额</view>
			<view class="price">
				<text class="unit">￥</text>
				<text class="num">{{(price/100).toFixed(2).split('.')[0]}}.</text>
				<text class="remainder">{{(price/100).toFixed(2).split('.')[1]}}</text>
			</view>
			<view class="remaining">
				<text class="remainingtext">剩余时间</text>
				<u-count-down :time="15 * 60 * 1000" format="mm:ss" @finish="$fn.jumpBack" />
			</view>
		</view>

		<view class="payType">
			<view class="pay_title">选择支付方式</view>
			<u-radio-group v-model="payType" placement="column">
				<view class="pay_item" @click="payType='1'">
					<view class="detail">
						<image class="payimg" src="../static/balanceicon.png" mode=""></image>
						<text class="balancepay">余额支付</text>
						<text class="balance">（余额 ￥{{(userInfo.balance/100).toFixed(2)}}）</text>
					</view>
					<u-radio name="1" />
				</view>
				<view class="pay_item" @click="payType='2'">
					<view class="detail">
						<image class="payimg" src="../static/wechaticon.png" mode=""></image>
						<text class="balancepay">微信支付</text>
						<!-- <text class="balance">（余额 ￥364.56）</text> -->
					</view>
					<u-radio name="2" />
				</view>
			</u-radio-group>
		</view>

		<view class="confirm_pay">
			<button class="pinkbtn" @click="confirmPayment">确认支付</button>
		</view>
	</view>
</template>

<script>
	export default {
		name: "Name",
		data() {
			return {
				payType: '1',
				price: 0,
				userInfo: {
					avatar: '',
					balance: 0,
					nickname: '',
					phone: ''
				},
				params: {
					appointmentTime: "",
					city: "",
					couponId: 0,
					orderType: 0,
					endLatAndLng: {
						lat: 0,
						lng: 0
					},
					petName: "",
					remark: "",
					sendAddress: "",
					sendName: "",
					sendPhone: "",
					startLatAndLng: {
						lat: 0,
						lng: 0
					},
					takeAddress: "",
					takeName: "",
					takePhone: ""
				}
			};
		},
		onLoad(option) {
			if (option.price) {
				this.price = Number(option.price)
			}
			if (option.params) {
				this.params = JSON.parse(option.params)
			}
			this.getUserInfoFn()
		},
		onShow() {},
		methods: {
			async confirmPayment() {
				this.params.appointmentTime = new Date(this.params.appointmentTime)
				console.log(this.params, '支付参数');
				if (this.payType == '1') {
					// 余额支付
					if (this.price > this.userInfo.balance) {
						return uni.showToast({
							icon: 'error',
							title: '余额不足'
						})
					} else {
						this.$api.balancePayment(this.params).then(res => {
							uni.redirectTo({
								url: '/pages/home/<USER>/paymentSuccess?price=' + this.price
							})
						})
					}

				} else {
					// 微信支付
					console.log('微信支付');
					try {
						uni.showLoading({
							mask: true
						})
						const res = await this.$api.wechatPayment(this.params)
						console.log(res, '参数');
						if (res.code == 200 && res.data.wechatPaySignVO) {
							console.log('我进来了');
							uni.requestPayment({
								provider: 'wxpay',
								timeStamp: res.data.wechatPaySignVO.timeStamp,
								nonceStr: res.data.wechatPaySignVO.nonceStr,
								package: res.data.wechatPaySignVO.package,
								signType: res.data.wechatPaySignVO.signType,
								paySign: res.data.wechatPaySignVO.paySign,
								success: (res) => {
									console.log('res', res);
									uni.showToast({
										title: '支付成功'
									})
									uni.redirectTo({
										url: '/pages/home/<USER>/paymentSuccess?price=' + this.price
									})
									uni.hideLoading()
								},
								fail: (err) => {
									console.log('err', err);
									uni.showToast({
										icon: 'error',
										title: '取消支付',
										duration: 1000
									})
									uni.hideLoading()
								},
							});
						} else if(!res.data.pay) {
							uni.showToast({
								icon: 'none',
								title: '支付成功'
							})
							uni.redirectTo({
								url: '/pages/home/<USER>/paymentSuccess?price=' + this.price
							})
							uni.hideLoading()
						} else {
							uni.showToast({
								icon: 'fail',
								title: res.msg
							})
							uni.hideLoading()
						}
						
					} catch (e) {
						//TODO handle the exception
						uni.showToast({
							icon: 'error',
							title: '支付失败'
						})
						uni.hideLoading()
					}
					

					


				}


			},
			// 查询余额
			getUserInfoFn() {
				this.$api.getUserInfo().then(res => {
					console.log(res, '信息');
					this.userInfo = res.data
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	::v-deep.u-count-down__text.data-v-463368ae {
		color: #FF3B72 !important;
	}

	.info {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 100rpx;

		.amount {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #303033;
			line-height: 33rpx;
		}

		.price {
			color: #303033;
			font-weight: bold;
			margin: 10rpx 0rpx;

			.unit {
				font-size: 56rpx;
			}

			.num {
				font-size: 96rpx;
			}

			.remainder {
				font-size: 56rpx;
			}

		}

		.remaining {
			display: flex;
			align-items: center;
			font-weight: 500;
			font-size: 28rpx;
			color: #303033;

			.remainingtext {
				margin-right: 10rpx;
			}

		}
	}

	.payType {
		padding: 100rpx 32rpx 0;

		.pay_title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #303033;
			line-height: 33rpx;
		}

		.pay_item {
			display: flex;
			justify-content: space-between;
			margin: 40rpx 0;

			.detail {
				display: flex;
				align-items: flex-end;

				.payimg {
					width: 40rpx;
					height: 40rpx;
				}

				.balancepay {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 36rpx;
					color: #18181A;
					line-height: 42rpx;
					margin-left: 30rpx;
				}

				.balance {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #303033;
					line-height: 33rpx;
				}

			}

		}
	}

	.confirm_pay {
		width: 686rpx;
		height: 96rpx;
		position: fixed;
		bottom: 68rpx;
		left: 50%;
		transform: translate(-50%);
	}
</style>