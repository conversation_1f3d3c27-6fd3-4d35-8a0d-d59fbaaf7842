<template>
	<view class="page">
		<c-scroll-list ref="list" @load="load" :api="api" :apiParams="apiParams">
			<view class="main" v-for="item in tableData" :key="item.date">
				<view class="date">{{item.date}}</view>
				<view class="record">
					<view class="item" v-for="v in item.list" :key="item.id">
						<view class="top">
							<view class="title">{{v.remark}}</view>
							<view class="price">+{{v.amount?(v.amount/100).toFixed(2):'0.00'}}</view>
						</view>
						<view class="bottom">
							<view class="text">{{v.createTime?$fn.parseTime(v.createTime):'暂无~'}}</view>
							<view class="text">实付金额：￥{{v.payAmount?(v.payAmount/100).toFixed(2):'0.00'}}</view>
						</view>
					</view>
				</view>
			</view>
		</c-scroll-list>
	</view>
</template>

<script>
	export default {
		name: "Name",
		data() {
			return {
				api: this.$api.getRechargeRecord,
				apiParams: {
					type: 2,
				},
				tableData: []
			};
		},
		onLoad() {},
		onShow() {},
		methods: {
			load(res) {
				console.log(res, '充值记录');
				this.tableData = res.list

				let month = []
				res.list.forEach(v => {
					let date = new Date(v.createTime).getFullYear() + '年' + (new Date(v.createTime).getMonth() +
						1) + '月'
					if (month.length == 0) {
						month.push({
							date,
							list: []
						})
					} else {
						if (month.findIndex(v => v.date == date) == -1) {
							month.push({
								date,
								list: []
							})
						}
					}

					month[month.findIndex(v => v.date == date)].list.push(v)

				})

				this.tableData = month
			},
		}
	};
</script>

<style lang="scss" scoped>
	.main {
		.date {
			width: 750rpx;
			padding: 16rpx 32rpx;
			box-sizing: border-box;
			background: #F8F9FA;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #909099;
			line-height: 33rpx;
		}

		.record {
			width: 750rpx;
			padding: 0 32rpx;
			box-sizing: border-box;

			.item {
				width: 100%;
				padding: 32rpx 0 24rpx;
				box-sizing: border-box;
				border-bottom: 1rpx solid #E6E6E6;

				&:nth-last-child(1) {
					border: none;
				}

				.top {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.title {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 28rpx;
						color: #18181A;
						line-height: 33rpx;
					}

					.price {
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 32rpx;
						color: #FF7FB5;
						line-height: 32rpx;
					}


				}

				.bottom {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 20rpx;

					.text {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #909099;
						line-height: 28rpx;
					}
				}


			}
		}
	}
</style>