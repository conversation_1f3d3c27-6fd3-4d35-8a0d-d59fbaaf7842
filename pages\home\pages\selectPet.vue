<template>
	<view class="page">
		<c-navBar title="选择" isTran isPerch :background="ceiling ? 'rgb(253, 220, 232)' : ''"></c-navBar>

		<view class="content">
			<scroll-view class="pet-list" scroll-y="true" style="height: calc(100vh - 400rpx)">
				<view v-if="petList.length === 0" class="flex flex-center" style="height: calc(100vh - 400rpx)">
					<u-empty text="暂无宠物数据" mode="data"></u-empty>
				</view>
				<view v-else class="pet-item" v-for="(pet, index) in petList" :key="pet.id" @click="selectPet(pet, index)" :class="{ 'selected': selectedIndex === index }">
					<view class="pet-avatar">
						<view class="avatar-circle">
							<text class="avatar-text">{{ pet.petName ? pet.petName.charAt(0) : '宠' }}</text>
						</view>
					</view>
					<view class="pet-info">
						<view class="pet-name flex align-center gap-8">
							<text>{{ pet.petName }}</text>
							<text class="mf-font-28" style="color: #6B7280;">{{ getPetTypeText(pet.petType) }}</text>
						</view>
						<view class="pet-details mf-font-28" style="color: #6B7280;">
							<view class="detail-row flex align-center gap-8">
								<text class="detail-label">体重：</text>
								<text class="detail-value">{{ pet.petWeight || '--' }}</text>
							</view>
							<view class="detail-row flex align-center gap-8">
								<text class="detail-label">偏好：</text>
								<text class="detail-value">{{ pet.preference || '无' }}</text>
							</view>
						</view>
					</view>
					<view class="select-icon">
						<view class="select-icon select flex flex-center" v-if="selectedIndex === index">
							<u-icon name="checkmark" size="32rpx" color="#fff"></u-icon>
						</view>
						<view class="select-icon flex flex-center" v-else>
							<u-icon name="checkmark" size="32rpx" color="#fff"></u-icon>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 底部确定按钮 -->
		<view class="bottom-bar">
			<button class="confirm-btn" @click="confirmSelect">确定</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			ceiling: false,
			selectedIndex: 0, // 默认选中第一个
			selectedPet: null,
			petList: [],
		}
	},
	onLoad(options) {
		this.handleGetPetList()
	},
	methods: {
		selectPet(pet, index) {
			this.selectedIndex = index;
			this.selectedPet = pet;
		},
		async handleGetPetList() {
			try {
				const res = await this.$api.getPetList()
				console.log(res, '宠物列表');
				this.petList = res.data || [];
				// 如果有宠物数据，默认选中第一个
				if (this.petList.length > 0) {
					this.selectedIndex = 0;
					this.selectedPet = this.petList[0];
				}
			} catch (error) {
				console.error('获取宠物列表失败:', error);
				uni.showToast({
					icon: 'none',
					title: '获取宠物列表失败'
				});
			}
		},
		// 宠物类型转换
		getPetTypeText(petType) {
			const typeMap = {
				'0': '猫',
				'1': '狗',
				0: '猫',
				1: '狗'
			};
			return typeMap[petType] || '宠物';
		},
		confirmSelect() {
			if (this.selectedPet) {
				// 构造与feedingOrder.vue期望的数据结构一致的对象
				const petData = {
					id: this.selectedPet.id,
					name: this.selectedPet.petName,
					type: this.getPetTypeText(this.selectedPet.petType),
					weight: this.selectedPet.petWeight,
					preference: this.selectedPet.preference
				};

				console.log('选中的宠物信息:', petData);

				// 将选中的宠物信息存储到本地存储
				uni.setStorageSync('selectedPet', petData);
				// 返回上一页
				uni.navigateBack();
			} else {
				uni.showToast({
					icon: 'none',
					title: '请选择一个宠物'
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: #F7F3F6;
	padding-bottom: 120rpx;
}

.content {

	padding: 24rpx;

}

.pet-list {
	.pet-item {
		background: #ffffff;
		border-radius: 24rpx;
		padding: 32rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		gap: 24rpx;

		.pet-avatar {
			width: 120rpx;
			height: 120rpx;

			.avatar-circle {
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
				background: linear-gradient(135deg, #FF80B5 0%, #FFB5D1 100%);
				display: flex;
				align-items: center;
				justify-content: center;

				.avatar-text {
					font-size: 48rpx;
					font-weight: 600;
					color: #ffffff;
				}
			}
		}

		.pet-info {
			flex: 1;

			.pet-name {
				font-size: 36rpx;
				font-weight: 600;
				color: #18181A;
				margin-bottom: 8rpx;
			}

			.pet-meta {
				font-size: 28rpx;
				color: #909099;
				margin-bottom: 16rpx;
			}

			.pet-details {
				display: flex;
				flex-direction: column;
				gap: 8rpx;
			}
		}

		.select-icon {
			background: #D0D0D0;
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
		}

		.select {
			background: #FF80B5;
		}
	}
}

.bottom-bar {
	padding: 24rpx;


	.confirm-btn {
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		left: 24rpx;
		right: 24rpx;
		box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(255, 128, 181, 0.7);
		height: 88rpx;
		background: #FF80B5;
		border-radius: 16rpx;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 600;
		border: none;

		&::after {
			border: none;
		}
	}
}
</style>
