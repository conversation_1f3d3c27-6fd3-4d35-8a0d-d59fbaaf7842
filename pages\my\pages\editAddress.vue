<template>
	<!-- 编辑收货地址 -->
	<view>
		<view class="pd20">
			<view class="df jcsb box aic">
				<text class="fs30 fwb">联系人</text>
				<view class="ce df aic">
					<input type="text" :maxlength="15" placeholder="联系人姓名" v-model="addressInfo.name">
					<!-- <img class="icon-back" src="/pages/my/static/myImg/fhyh.png" alt=""> -->
				</view>
			</view>
			<view class="df jcsb box aic">
				<text class="fs30 fwb">电话</text>
				<view class="ce df aic">
					<input type="text" :maxlength="15" placeholder="联系人电话" v-model="addressInfo.phone">
					<!-- <img class="icon-back" src="/pages/my/static/myImg/fhyh.png" alt=""> -->
				</view>
			</view>
			<view class="df jcsb box aic">
				<text class="fs30 fwb">地址</text>
				<view class="ce df aic" @click="$fn.jumpPage('/pages/home/<USER>/selectAddress')">
					<view class="addrssName">{{addressInfo.address?addressInfo.address:'选择地址'}}</view>
					<img class="icon-back" src="/static/common/go.png" alt="">
				</view>
			</view>
			<view class="df jcsb box aic">
				<text class="fs30 fwb">门牌号</text>
				<view class="ce df aic">
					<input type="text" style="width: 450rpx;" placeholder="单元、楼层、门牌号等，如:2单元301"
						v-model="addressInfo.house">
					<!-- <img class="icon-back" src="/pages/my/static/myImg/fhyh.png" alt=""> -->
				</view>
			</view>
			<view class="df btnBox">
				<!-- <button class="setAddress" @click="defaultBtn">设为默认地址</button>
				<button class="save" @click="saveBtn">保存</button> -->
				<button class="pinkbtn" @click="saveBtn">保存</button>
			</view>
		</view>

		<!-- 地区选择器 -->
		<!-- 选择地区 -->
		<u-picker :show="showArea" ref="uPicker" :columns="areaList" keyName="name" @confirm="areaConfirm"
			@change="changeHandler" @cancel='showArea=false'></u-picker>
		</u-picker>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				show: false,
				showArea: false,
				addressInfo: {
					address: "", //地址
					house: "", //门牌号
					lat: 0, //纬度
					lng: 0, //经度
					name: "", //联系人
					phone: "", //联系电话
					type: 1, //1-起点 2-终点
				},
				returnAddress: '请选择地区',
				Id: '',
				page: 10,
				showpage: 1,
				title: '新增',
				areaList: []
			}
		},
		onLoad: function(option) {
			console.log(option.v, 'option.v');
			if (option.v != 'null') {
				this.addressInfo = JSON.parse(option.v)
				uni.setNavigationBarTitle({
					title: "编辑地址"
				})
			}
		},
		onShow() {
			// 获取传回来的本地地址信息
			this.getAddress()
		},
		methods: {

			// 获取传回来的本地地址信息
			getAddress() {
				const addressInfo = uni.getStorageSync('address')
				if (addressInfo) {
					console.log(addressInfo, 'addressInfo');
					this.addressInfo.address = addressInfo.district + addressInfo.name
					this.addressInfo.lng = addressInfo.location.split(',')[0]
					this.addressInfo.lat = addressInfo.location.split(',')[1]
					uni.removeStorageSync('address')
				}
			},



			// 切换地区
			changeHandler(e, mode = false) {
				this.$api.getAreaQ({
					pid: e.value[0].id
				}).then(res => {
					this.columnData = res.data
					const {
						columnIndex,
						value,
						values, // values为当前变化列的数组内容
						index,
						// 微信小程序无法将picker实例传出来，只能通过ref操作
						picker = this.$refs.uPicker
					} = e
					// 当第一列值发生变化时，变化第二列(后一列)对应的选项
					if (columnIndex === 0 || mode) {
						// console.log('this.columnData[index]',this.columnData[index]);
						// picker为选择器this实例，变化第二列对应的选项

						picker.setColumnValues(1, this.columnData)
						// picker.setColumnValues(1, this.columnData[index])
					}
					// console.log(this.columnData,'this.columnData')
				})
			},
			// 地区选择
			areaSelection(e) {},
			// 保存按钮
			saveBtn() {
				if (!this.addressInfo.name) {
					uni.showToast({
						icon: 'none',
						title: "请输入联系人名称"
					})
					return
				} else if (!this.$fn.phoVerify(this.addressInfo.phone)) {
					uni.showToast({
						icon: 'none',
						title: "请输入手机号"
					})
					return
				} else if (!this.addressInfo.address) {
					uni.showToast({
						icon: 'none',
						title: "请选择地址"
					})
					return
				} else if (!this.addressInfo.house) {
					uni.showToast({
						icon: 'none',
						title: "请输入详细地址"
					})
					return
				}



				console.log(this.addressInfo, 'this.addressInfo');
				this.$api.editAddress(this.addressInfo).then(res => {
					console.log(res, '保存成功');
					uni.showToast({
						title: '操作成功'
					})
					uni.navigateBack()
				})
			},
			// 获取地址
			getAddreesData() {},
			// 设置为默认按钮
			defaultBtn() {},
		},

	}
</script>

<style lang="scss" scoped>
	.pd20 {
		padding: 0 20rpx;
	}

	.top_nav2 {
		position: relative;
		z-index: 10;

		.nav {
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
		}

		// .title{
		// 	background: red;
		// }
		.nav_back {
			width: 16rpx;
			height: 28rpx;
			position: absolute;
			top: 30rpx;
			left: 30rpx;
		}
	}

	.box {
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #F0F1F5;
		border-radius: 10rpx;
		font-size: 26rpx;

		.ce {
			margin-right: 20rpx;
			color: #909299;

			input {
				text-align: right;
			}
		}

		.fs30 {
			font-size: 30rpx;
		}

		.icon {
			width: 30rpx;
			height: 30rpx;
		}

		.icon-back {
			width: 24rpx;
			height: 24rpx;
			margin-left: 20rpx;
		}

		.headImg {
			width: 80rpx;
			height: 80rpx;
		}

	}

	.btnBox {
		margin-top: 200rpx;

		.setAddress {
			width: 320rpx;
			height: 80rpx;
			border-radius: 40rpx;
			background: #FF802F;
			color: #fff;
			font-size: 32rpx;
			font-weight: bold;
		}

		.save {
			width: 320rpx;
			height: 80rpx;
			border-radius: 40rpx;
			background: #ED1010;
			color: #fff;
			font-size: 32rpx;
			font-weight: bold;
		}
	}

	.addrssName {
		text-align: right;
		width: 450rpx;
		/* 强制文本在一行内显示 */
		white-space: nowrap;
		/* 溢出内容隐藏 */
		overflow: hidden;
		/* 文本溢出显示省略号 */
		text-overflow: ellipsis;
	}
</style>