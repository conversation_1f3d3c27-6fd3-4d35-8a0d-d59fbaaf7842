<template>
	<view class="main">
		<view @click="uploadAvatar" class="item">
			<text class="text">头像</text>
			<view class="item-right">
				<image class="head" v-if="form.avatar" :src="vuex_imgUrl+form.avatar" mode="aspectFill"></image>
				<image class="head" v-if="!form.avatar" src="../../../static/tabbar/hander.png"></image>
				<u-icon name="arrow-right" color="#666666" size="20rpx"></u-icon>
			</view>
		</view>

		<view class="items item">
			<text class="text">昵称</text>
			<view class="item-right">
				<input class="ipt" v-model="form.nickname" placeholder="请输入" />
			</view>
		</view>
		<view class="items item">
			<text class="text">爱宠名称</text>
			<view class="item-right">
				<input class="ipt" v-model="form.petName" placeholder="请输入" />
			</view>
		</view>
		<!-- 	<view class="items item" @click="select_sex">
			<text class="text">性别</text>
			<view class="item-right">
				<view class="ipt">
					{{ form.sex==0?'女':'' }}
					{{ form.sex==1?'男':'' }}
				</view>
				<u-icon name="arrow-right" color="#666666" size="20rpx"></u-icon>
			</view>
		</view> -->
		<view class="items item">
			<text class="text">手机号码</text>
			<view class="item-right">
				<input class="ipt" v-model="form.phone" placeholder="请输入手机号" />
			</view>
		</view>
		<!-- 		<view class="items mt40">
			<view class="text">个性签名</view>
			<view class="mt20">
				<u--textarea v-model="form.description" height="200rpx" placeholder="请输入内容"></u--textarea>
			</view>
		</view> -->
		<!-- 按钮 -->
		<view @click="submit" class="footer">
			<view class="footer-btn">保存</view>
		</view>

		<!-- 选择性别 -->
		<u-picker :show="show" :columns="sexColumns" @confirm='selected_sex' @close="show=false"
			keyName="name"></u-picker>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false, //性别选择
				sexColumns: [
					[{
							name: '女',
							type: 0
						},
						{
							name: '男',
							type: 1
						},
					]
				],
				regionShow: false, //地区选择
				form: {
					avatar: '', //头像
					nickname: '', //昵称
					petName: '', //爱宠昵称
					// sex: 0, //性别1=男，0=女
					phone: '', //手机号
					// description: '',
				},
			}
		},
		methods: {
			select_sex() {
				this.show = true
			},
			selected_sex(list) {
				// console.log(list);
				this.form.sex = list.value[0].type
				this.show = false
			},
			// 上传头像
			uploadAvatar() {
				console.log(11);
				uni.chooseImage({
					count: 1, //默认9
					success: (res) => {
						uni.uploadFile({
							url: this.vuex_baseUrl + '/common/upload',
							filePath: res.tempFilePaths[0],
							name: 'file',
							success: (uploadFileRes) => {
								console.log(uploadFileRes)
								let imgData = JSON.parse(uploadFileRes.data)
								console.log(imgData)
								if (imgData.code == 200) {
									uni.showToast({
										mask: true,
										icon: 'none',
										title: '上传成功'
									})
									this.form.avatar = imgData.fileName
								}
							}
						});
					},
					fail: (res) => {
						console.log(res);
					}
				});
			},
			getInfo() {
				this.$api.getUserInfo().then(res => {
					console.log(res, '信息');
					this.form = res.data
				})

			},
			submit() {
				if (this.form.name == '') {
					uni.showToast({
						mask: true,
						icon: 'none',
						title: '昵称不能为空'
					})
					return
				}

				this.$api.updateUser(this.form).then(res => {
					uni.showToast({
						icon: 'success',
						title: '保存成功',
						success() {
							uni.navigateBack()
						}
					})
				})
			}
		},
		onLoad() {
			console.log()
			this.getInfo()
		}

	}
</script>

<style lang="scss" scoped>
	.main {
		padding: 0 30rpx 178rpx;
	}

	.head {
		width: 90rpx;
		height: 90rpx;
		border-radius: 45rpx;
		margin-right: 10rpx;
	}

	.item {
		display: flex;
		justify-content: space-between;
		border-bottom: 2rpx solid #EEEEEE;
		height: 140rpx;
		align-items: center;

	}

	.items {
		height: 110rpx;
	}

	.item-right {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.text {
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
	}

	.ipt {
		width: 180rpx;
		font-size: 24rpx;
		color: #999999;
		margin-right: 8rpx;
		text-align: right;
	}

	.ipt1 {
		width: 500rpx;
	}

	::v-deep .u-textarea {
		background-color: #F7F7F7 !important;
	}

	.footer {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		padding: 0 40rpx 68rpx;
		// --iphonex-fix-bottom: env(safe-area-inset-bottom);
		// padding-bottom: var(--iphonex-fix-bottom);
	

		view {
			width: 670rpx;
			height: 96rpx;
			line-height: 96rpx;
			text-align: center;
			color: #fff;
			font-size: 36rpx;
			font-weight: bold;
			background: $c-bgColor;
			border-radius: 16rpx;
		}
	}
</style>