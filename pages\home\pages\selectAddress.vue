<template>
	<view class="page">
		<c-navBar title="选择地址" isPerch></c-navBar>
		<view class="map">
			<view class="search">
				<view class="city">
					{{city?city:'成都市'}}
					<!-- <image class="dropDown" src="../static/dropDown.png" mode=""></image> -->
				</view>
				<view class="input">
					<u--input placeholder="请输入地址" border="none" v-model="searchAddress" prefixIcon="search"
						prefixIconStyle="font-size: 22px;color: #909399" @input="mapInput" />
				</view>
			</view>
			<map id="homeMap" style="width: 100%; height: 50vh;" :latitude="latitude" :longitude="longitude"
				:markers="markers" @tap="selectLocation" />
			<!-- 回到原点 -->
			<image @click="resetMap" class="positioning" src="../../../static/tabbar/positioning.png" mode="">
			</image>
		</view>
		<view class="address_detail" :style="'height: calc(50vh - '+(statusHeight+titleHeight)+'px);'">
			<view class="address">
				<view class="address_item" v-for="(item,index) in mapTips" :key="index" @click="goBack(item)">
					<view class="address_title">
						<text class="name single-line-ellipsis">{{item.name}}</text><text
							class="distance">{{item.distance?'&lt;'+item.distance:''}}</text>
					</view>
					<view class="address_info single-line-ellipsis">
						{{item.district+item.address}}
					</view>
				</view>
			</view>
		</view>
		<!-- <c-selectDistrict :show="addressShow" type='2' @confirm="addressConfirm"
			@cancel="addressShow=false"></c-selectDistrict> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				myLocation: {
					latitude: 30.575917,
					longitude: 104.068103,
					address: {
						street: '成都市'
					}
				},

				id: 1, // 使用 marker点击事件 需要填写id
				title: 'map',
				latitude: 30.575917,
				longitude: 104.068103,
				markers: [{
					id: 1,
					latitude: 30.575917,
					longitude: 104.068103,
					iconPath: '../../../static/tabbar/coordinates.png',
					width: 16,
					height: 30,
					callout: {
						content: '成都市',
						borderRadius: 10,
						bgColor: '#FF80B5',
						display: 'ALWAYS',
						color: '#fff',
						padding: '5'
					}
				}],

				statusHeight: 0,
				titleHeight: 0,
				searchAddress: '',
				mapTips: [],
				addressShow: false,
				// city: {}
				city: ''
			};
		},
		onLoad() {
			this.getPhe()

		},
		onShow() {

			this.getLocationFn()
		},
		methods: {

			// 获取定位
			getLocationFn() {
				uni.getLocation({
					type: 'gcj02',
					altitude: true,
					isHighAccuracy: true,
					success: (res) => {
						console.log(res, '当前位置');
						let location = [res.longitude, res.latitude].join(',')
						console.log(location, 'location');
						this.$amapFun.getRegeo({
							location,
							success: (data) => {
								//成功回调
								console.log(data, '我的位置信息');
								this.myLocation.latitude = data[0].latitude
								this.myLocation.longitude = data[0].longitude
								this.myLocation.address.street = data[0].name
								this.latitude = data[0].latitude
								this.longitude = data[0].longitude
								this.markers[0].latitude = data[0].latitude
								this.markers[0].longitude = data[0].longitude
								this.markers[0].callout.content = data[0].name
								this.city = data[0].regeocodeData.addressComponent.city
								this.searchInfo()
							},
							fail: (info) => {
								//失败回调
								console.log(info)
								if (info.errCode == '10044') {
									uni.showToast({
										icon: 'none',
										title: '用户每日查询超过限制'
									})
								}
								this.searchInfo()
							},

						})
					},
					fail: (err) => {
						this.$fn.getSetting()
						console.log('我没办法获取定位', err);
					}
				});
			},



			// 获取手机导航栏高度
			getPhe() {
				// 状态栏高度
				let systemInfo = uni.getSystemInfoSync()
				this.statusHeight = systemInfo.safeArea.top
				// app端的标题栏高度
				this.titleHeight = 46
				// 小程序端的标题栏的高度
				// #ifdef MP-WEIXIN
				let titleSize = wx.getMenuButtonBoundingClientRect()
				this.titleHeight = titleSize.height + (titleSize.top - systemInfo.safeArea.top) * 2
				// #endif
			},
			// 改变markers[0]数据
			changeMarkers(longitude, latitude, desc) {
				this.markers[0].longitude = longitude
				this.markers[0].latitude = latitude
				this.markers[0].callout.content = desc
			},

			// 回到原点
			resetMap() {
				// 修改成当前定位信息
				this.$fn.resetLocation(this.myLocation.longitude, this.myLocation.latitude, 'homeMap')
				this.changeMarkers(this.myLocation.longitude, this.myLocation.latitude, this.myLocation.address.street)
				this.longitude = this.myLocation.longitude
				this.latitude = this.myLocation.latitude
				this.searchAddress = ''
				this.searchInfo()
			},

			// 选择地图定位
			selectLocation(e) {
				// return //弃用 如需使用去掉return
				console.log(e, '选择定位信息');
				this.searchAddress = ''
				let location = [e.detail.longitude, e.detail.latitude].join(',')
				console.log(location, 'location');
				this.$amapFun.getRegeo({
					location,
					success: (data) => {
						//成功回调
						console.log(data, '我的位置信息');
						this.changeMarkers(e.detail.longitude, e.detail.latitude, data[0].desc)
						this.searchAddress = data[0].desc
						this.searchInfo(1)
					},
					fail: (info) => {
						//失败回调
						if (info.errCode == '10044') {
							uni.showToast({
								icon: 'none',
								title: '用户每日查询超过限制'
							})
						}
						console.log(info)
					}
				})
			},
			// 输入定位
			mapInput(e) {
				if (e) {
					uni.$u.debounce(this.searchInfo, 500)
					// this.changeOrientation()
				} else {
					uni.$u.debounce(this.searchInfo, 500)
					// this.changeOrientation()
					this.mapTips = []
				}
			},
			// 查询定位提示
			searchInfo(type) {
				console.log('我进来查询了', this.city);
				console.log('我进来查询了名称', this.searchAddress ? this.searchAddress : this.myLocation.address.street);
				this.$amapFun.getInputtips({
					keywords: this.searchAddress ? this.searchAddress : this.myLocation.address.street,
					location: '',
					city: this.city,
					success: data => {
						if (data && data.tips) {
							console.log(data.tips, 'data.tips');
							this.mapTips = data.tips.map(v => {
								console.log(v.location?.length, 'v.location?.length');
								if (v.location?.length > 0) {
									v.distance = this.getDistance(this.latitude, this.longitude, v
										.location.split(',')[1], v.location.split(',')[0])
								} else {
									v.distance = ''
								}
								return v
							})
							console.log(this.mapTips, 'this.mapTips');
						}

						if (data && data.tips) {
							console.log(data.tips, '改变');
							data.tips.some(v => {
								if (v.location?.length > 0 && v.name) {
									if (type != 1) {
										this.changeMarkers(v.location.split(',')[0], v.location
											.split(',')[1], v.name)
									}
									this.longitude = v.location.split(',')[0]
									this.latitude = v.location.split(',')[1]
									return true;
								}
								return false;
							})
						}

					},
					fail: (info) => {
						//失败回调
						console.log(info, '我进来了');
						if (info.errCode == '10003') {
							uni.showToast({
								icon: 'none',
								title: '用户每日查询超过限制'
							})
						}
					}
				})
			},

			// 改变地图上的定位
			// changeOrientation() {
			// 	this.$amapFun.getInputtips({
			// 		keywords: this.searchAddress,
			// 		location: '',
			// 		city: '成都市',
			// 		success: data => {
			// 			if (data && data.tips) {
			// 				console.log(data.tips, '改变');
			// 				data.tips.some(v => {
			// 					if (v.location && v.name) {
			// 						this.changeMarkers(v.location.split(',')[0], v.location
			// 							.split(',')[1], v.name)
			// 						this.longitude = v.location.split(',')[0]
			// 						this.latitude = v.location.split(',')[1]
			// 						return true;
			// 					}
			// 					return false;
			// 				})
			// 			}

			// 		}
			// 	})
			// },

			// 计算距离
			getDistance(lat1, lon1, lat2, lon2) {
				const rad = Math.PI / 180;
				const R = 6378137; // 地球的半径，单位为米
				const a = Math.sin(lat1 * rad) * Math.sin(lat2 * rad) + Math.cos(lat1 * rad) * Math.cos(lat2 * rad) * Math
					.cos((lon2 - lon1) * rad);
				let distance = R * Math.acos(a);
				if (distance > 1000) {
					distance = (distance / 1000).toFixed(1) + 'km'
				} else {
					distance = distance.toFixed(0) + 'm'
				}
				return distance;
			},

			// 选择城市
			addressConfirm(e) {
				this.addressShow = false
				console.log('e', e);
				this.city = e
			},

			goBack(v) {
				console.log(v, 'v.latitude');
				if (!v.location || v.location.length == 0) {
					return uni.showToast({
						title: '请选择具体地址',
						icon: 'none'
					})
				}
				console.log(v, '选择的地址');
				uni.setStorageSync('address', v)
				uni.navigateBack()
			}

		}
	};
</script>

<style lang="scss" scoped>
	.map {
		position: relative;

		.search {
			position: absolute;
			top: 30rpx;
			width: 750rpx;
			padding: 0 32rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			justify-content: space-between;
			z-index: 999;

			.city {
				height: 40rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #18181A;
				line-height: 33rpx;
				text-align: left;
				display: flex;
				align-items: center;

				.dropDown {
					width: 20rpx;
					height: 14rpx;
					margin-left: 10rpx;
				}
			}

			.input {
				width: 560rpx;
				height: 72rpx;
				background: #FFFFFF;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
				display: flex;
				align-items: center;
				padding: 0 20rpx;
				box-sizing: border-box;
			}

		}

		.positioning {
			width: 80rpx;
			height: 80rpx;
			position: absolute;
			right: 40rpx;
			bottom: 90rpx;
		}
	}


	.address_detail {
		width: 750rpx;
		position: relative;

		.address {
			width: 750rpx;
			height: calc(100% + 50rpx);
			background: #FFFFFF;
			border-radius: 24rpx 24rpx 0rpx 0rpx;
			position: absolute;
			top: -50rpx;
			overflow: scroll;


			.address_item {
				width: 100%;
				height: 146rpx;
				border-bottom: 1rpx solid #F0F0F0;
				padding: 0 32rpx;
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				justify-content: center;


				.address_title {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.name {
						width: 85%;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 32rpx;
						color: #18181A;
						line-height: 38rpx;
					}

					.distance {
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 24rpx;
						color: #56B7F5;
						line-height: 28rpx;
						text-align: right;
					}
				}

				.address_info {
					width: 100%;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #909099;
					line-height: 28rpx;
					margin-top: 20rpx;
				}
			}
		}
	}
</style>