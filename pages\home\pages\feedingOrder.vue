<template>
	<view class="page">
		<view class="headBg">
			<!-- 商家信息卡片 -->
			<view class="card">
				<view class="store-info">
					<view class="flex align-center gap-16">
						<view class="mf-font-32 mf-weight-bold">{{ feedAdrs.name }}</view>
						<image style="width: 32rpx; height: 32rpx;" src="/pages/home/<USER>/call.png" mode="" />
					</view>
					<view class="flex align-center gap-16" style="margin-top: 16rpx;">
						<text class="mf-font-24" style="color: #9B9B9B;">{{ feedAdrs.address }}</text>
						<image style="width: 32rpx; height: 32rpx;" src="/pages/home/<USER>/point.png" mode="" />

					</view>
				</view>
				<u-line margin="0.5em 0" />
				<view class="flex align-center justify-between" style="margin-top: 16rpx;">
					<view class="mf-font-28 mf-weight-bold" style="color: #000000;">预约时间</view>
					<view class="flex align-center gap-8">
						<view class="mf-font-24" style="color: #0F6EFF;">{{ feedDate.label }}</view>
						<!-- <u-icon name="arrow-right" size="24rpx" color="#0F6EFF"></u-icon> -->
					</view>
				</view>
			</view>


			<!-- 宠物信息卡片 -->
			<view class="card">
				<view class="form bdbt" @click="goSelectPet">
					<view class="title">宠物名称</view>
					<view class="centent">
						<text :class="petName ? '' : 'placeholder'">{{ petName || '去选择' }}</text>
						<image class="icon_r" src="../../../static/tabbar/goNext.png" mode="" />
					</view>
				</view>
				<view class="form bdbt" @click="goSelectService" :class="{ 'disabled': !petName }">
					<view class="title">服务项目</view>
					<view class="centent">
						<view class="service-info">
							<text :class="serviceType ? '' : 'placeholder'">{{ serviceType || '去选择' }}</text>
							<!-- <text v-if="selectedServiceData" class="service-price">￥{{ selectedServiceData.price }}</text> -->
						</view>
						<image class="icon_r" src="../../../static/tabbar/goNext.png" mode="" />
					</view>
				</view>
				<view class="form bdbt">
					<view class="flex flex-col gap-16" style="width: 100%;">
						<view class="form-title">订单备注</view>
						<view class="textarea-container">
							<u--textarea v-model="remarks" placeholder="请输入" maxlength="200"></u--textarea>
						</view>
					</view>
				</view>
			</view>

			<!-- 优惠券卡片 -->
			<view class="card">
				<view class="form bdbt" @click="goSelectCoupon" :class="{ 'disabled': !petName || !selectedServiceData }">
					<view class="title">优惠券</view>
					<view class="centent">
						<text class="coupon" :class="coupon.amount ? 'coupon_have' : ''">
							{{ coupon.amount ? '-￥' + (coupon.amount / 100).toFixed(2) : '选择优惠券' }}
						</text>
						<image class="icon_r" src="../../../static/tabbar/goNext.png" mode="" />
					</view>
				</view>
				<view class="form">
					<text class="total-label">合计费用</text>
					<text class="total-price" style="color: #FF80B5;">￥{{ finalAmount }}</text>
				</view>
			</view>

			<!-- 协议 -->
			<view class="agreement-box">
				<view class="ag-box" @click="tabAgree">
					<image v-if="isAgree" src="/static/common/select.png" mode=""></image>
					<image v-else src="/static/common/noSelect.png" mode=""></image>
				</view>
				<view class="agreement">
					<text @click.stop="tabAgree">我已阅读并同意</text>
					<text class="primary-color"
						@click.stop="$fn.jumpPage(`/pages/tabbar/agreement?type=3`)">《支付协议》</text>
				</view>
			</view>
		</view>

		<!-- 底部下单按钮 -->
		<view class="bottom-bar">
			<button class="order-btn" @click="submitOrder">立即下单</button>
		</view>

		<!-- 时间选择器 -->
		<c-dateTime-picker v-if="orderTimeShow" :city="myCity" :show.sync="orderTimeShow" @confirm="orderTimeConfirm"></c-dateTime-picker>
	</view>
</template>

<script>
export default {
	data() {
		return {
			ceiling: false,
			orderTimeShow: false,
			petName: '',
			serviceType: '',
			selectedServiceData: null, // 存储完整的服务数据
			remarks: '',
			myCity: '',
			isAgree: false, // 协议同意状态
			coupon: {
				amount: 0
			}, // 优惠券
			baseAmount: 0, // 基础费用（分）
			totalAmount: 0,
			finalAmount: '0',

			feedAdrs: {},
			feedDate: {},
			orderType: 1
		}
	},
	onLoad(options) {
		if (options) {
			let { date, feedAdrs, orderType } = options
			if (date) {
				this.feedDate = JSON.parse(date)
			}
			if (feedAdrs) {
				this.feedAdrs = JSON.parse(feedAdrs)
			}
			if (orderType) {
				this.orderType = orderType
			}
		}
		// 获取城市信息
		this.myCity = uni.getStorageSync('city');
		// 初始化价格计算
		this.calculatePrice();
	},
	onShow() {
		// 从本地存储获取选择的宠物和服务
		const selectedPet = uni.getStorageSync('selectedPet');
		if (selectedPet) {
			this.petName = selectedPet.name + ' ' + selectedPet.type;
			uni.removeStorageSync('selectedPet');
		}

		const selectedService = uni.getStorageSync('selectedService');
		if (selectedService) {
			this.serviceType = selectedService.name;
			this.selectedServiceData = selectedService; // 保存完整的服务数据
			// 更新基础价格（假设后端返回的价格是以元为单位，需要转换为分）
			this.baseAmount = Math.round(parseFloat(selectedService.price) * 100);
			uni.removeStorageSync('selectedService');
			// 重新计算价格
			this.calculatePrice();
		}

		// 获取本地存储的优惠券
		const coupon = uni.getStorageSync('coupon');
		if (coupon) {
			this.coupon = coupon;
			uni.removeStorageSync('coupon');
			// 重新计算价格
			this.calculatePrice();
		}
	},
	methods: {
		// 时间确认
		orderTimeConfirm(v) {
			this.orderTimeShow = false;
			this.orderDate = v;
		},

		// 跳转到宠物选择页面
		goSelectPet() {
			this.$fn.jumpPage('/pages/home/<USER>/selectPet');
		},

		// 跳转到服务选择页面
		goSelectService() {
			if (!this.petName) {
				return uni.showToast({
					icon: 'none',
					title: '请先选择宠物',
					duration: 2000
				});
			}
			this.$fn.jumpPage('/pages/home/<USER>/selectService');
		},

		// 跳转到优惠券选择页面
		goSelectCoupon() {
			if (!this.petName) {
				return uni.showToast({
					icon: 'none',
					title: '请先选择宠物',
					duration: 2000
				});
			}
			if (!this.selectedServiceData) {
				return uni.showToast({
					icon: 'none',
					title: '请先选择服务项目',
					duration: 2000
				});
			}
			// 向优惠券选择页面传递价格（参考confirmOrder.vue的做法：总价+已有优惠券金额）
			let orderPrice = this.totalAmount + (this.coupon.amount || 0);
			this.$fn.jumpPage(`/pages/home/<USER>/selectCoupon?city=${this.myCity}&orderPrice=${orderPrice}`);
		},

		// 同意/不同意协议
		tabAgree() {
			this.isAgree = !this.isAgree
		},

		// 计算价格
		calculatePrice() {
			// 如果没有选择服务，价格为0
			if (!this.selectedServiceData) {
				this.totalAmount = 0;
				this.finalAmount = '0.00';
				return;
			}
			
			// 基础价格（单位：分）
			let totalAmount = this.baseAmount;
			
			// 优惠券金额（假设也是分为单位，与confirmOrder.vue保持一致）
			let couponAmount = this.coupon.amount ? this.coupon.amount : 0;
			
			// 计算最终价格（减去优惠券金额，都是分为单位）
			let finalAmountInCents = Math.max(0, totalAmount - couponAmount);
			
			// 更新显示价格（转换为元）
			this.totalAmount = totalAmount;
			this.finalAmount = (finalAmountInCents / 100).toFixed(2);
			
			console.log('价格计算：', {
				baseAmount: this.baseAmount,
				couponAmount,
				finalAmountInCents,
				finalAmount: this.finalAmount
			});
		},

		// 提交订单
		submitOrder() {
			// 验证必填字段
			if (!this.petName) {
				return uni.showToast({
					title: '请选择宠物名称',
					icon: 'none',
					duration: 2000
				});
			}
			if (!this.selectedServiceData) {
				return uni.showToast({
					title: '请选择服务项目',
					icon: 'none',
					duration: 2000
				});
			}
			if (!this.feedAdrs.address) {
				return uni.showToast({
					title: '请选择服务地址',
					icon: 'none',
					duration: 2000
				});
			}
			if (!this.feedDate.value) {
				return uni.showToast({
					title: '请先选择预约时间',
					icon: 'none',
					duration: 2000
				});
			}
			if (!this.isAgree) {
				return uni.showToast({
					title: '请先查看并同意协议',
					icon: 'none',
					duration: 2000
				});
			}

			// 构建订单参数，参考 confirmOrder.vue 的格式
			let dateArr = this.feedDate.value.split(' ')
			let appointmentTime = this.$fn.parseTime(dateArr[0], '{y}-{m}-{d}' + ' ' + dateArr[1] + ':00')
			console.log(new Date(appointmentTime), '预约时间');

			let params = {
				appointmentTime,
				city: this.myCity,
				couponId: this.coupon.id,
				orderType: 1, // 喂养订单
				petName: this.petName,
				remark: this.remarks,
				serviceId: this.selectedServiceData.id, // 服务ID
				serviceName: this.selectedServiceData.name, // 服务名称
				servicePrice: this.selectedServiceData.price, // 服务价格
				sendAddress: this.feedAdrs.address + (this.feedAdrs.house || ''),
				sendName: this.feedAdrs.name,
				sendPhone: this.feedAdrs.phone,
				sendLatAndLng: {
					lat: this.feedAdrs.lat,
					lng: this.feedAdrs.lng
				}
			}

			console.log('喂养订单参数:', params);

			// 跳转到支付页面，传递最终支付金额（支付页面期望接收分为单位）
			let finalAmountInCents = Math.max(0, this.baseAmount - (this.coupon.amount || 0));
			console.log('跳转支付页面，价格(分):', finalAmountInCents, '价格(元):', this.finalAmount);
			this.$fn.jumpPage(`/pages/home/<USER>/payOrder?price=${finalAmountInCents}&params=${JSON.stringify(params)}`);
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: calc(100vh - 144rpx);
	background: linear-gradient(180deg, #FFE8F4 0%, #F7F3F6 50%, #F7F3F6 100%);
	padding-top: 24rpx;
	padding-bottom: 120rpx;

}

.headBg {
	padding: 0 24rpx;
}

.card {
	background: #ffffff;
	border-radius: 24rpx;
	margin-bottom: 20rpx;
	padding: 28rpx 32rpx;
}

.appointment_time {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.title {
		font-size: 32rpx;
		color: #18181A;
		font-weight: 500;
	}

	.time {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.date {
			font-size: 28rpx;
			color: #0F6EFF;
			font-weight: 500;
		}

		.icon_r {
			width: 12rpx;
			height: 20rpx;
		}
	}
}

.form {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 0;

	&.bdbt {
		border-bottom: 1rpx solid #F5F5F5;
	}

	&.disabled {
		opacity: 0.5;
		pointer-events: none;
	}

	.title {
		font-size: 32rpx;
		color: #18181A;
		font-weight: 500;
	}

	.centent {
		display: flex;
		align-items: center;
		gap: 16rpx;

		text {
			font-size: 28rpx;
			color: #18181A;

			&.placeholder {
				color: #999999;
			}
		}

		.icon_r {
			width: 12rpx;
			height: 20rpx;
		}
	}
}

.form-title {
	font-size: 32rpx;
	color: #18181A;
	font-weight: 500;
	margin-bottom: 20rpx;
}

.textarea-container {
	min-height: 120rpx;
}

.coupon {
	&.coupon_have {
		color: #FF80B5 !important;
	}
}

.placeholder {
	color: #999999 !important;
}

.service-info {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4rpx;
	
	.service-price {
		font-size: 24rpx;
		color: #FF80B5;
		font-weight: 500;
	}
}

.total-amount {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx;
	background: #ffffff;
	border-radius: 24rpx;
	margin-bottom: 20rpx;

	.total-label {
		font-size: 32rpx;
		color: #18181A;
		font-weight: 500;
	}

	.total-price {
		font-size: 36rpx;
		color: #FF80B5;
		font-weight: 600;
	}
}

.agreement-box {
	display: flex;
	align-items: center;
	width: 702rpx;
	margin: 20rpx auto 0;

	.ag-box {
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	image {
		width: 24rpx;
		height: 24rpx;
	}

	.agreement {
		font-size: 28rpx;
		font-weight: 500;
		color: #666666;

		.primary-color {
			color: #56B7F5;
		}
	}
}

.bottom-bar {
	padding: 24rpx;


	.order-btn {
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		left: 24rpx;
		right: 24rpx;
		box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(255,128,181,0.7);
		height: 88rpx;
		background: #FF80B5;
		border-radius: 16rpx;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 600;
		border: none;

		&::after {
			border: none;
		}
	}
}
</style>
