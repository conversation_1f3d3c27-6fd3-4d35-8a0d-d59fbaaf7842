<template>
	<view class="page">
		<view class="distribution">
			<view class="icon_l " :class="type=='1'?'pickup_icon':'send_icon'">{{type=='1'?'接':'送'}}</view>
			<view class="info">
				<view class="address single-line-ellipsis">{{distributionData.address}}</view>
				<view class="user">
					<text class="username">{{distributionData.name}}</text> <text>{{distributionData.phone}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "c-distribution",
		props: {
			type: {
				type: String,
				default: '1'
			},
			distributionData: {
				type: Object,
				default: {
					address: '成都茂业中心B座XXXX',
					name: '张语嫣',
					phone: '15808088888'
				}
			}
		},
		data() {
			return {};
		},
		onLoad() {},
		onShow() {},
		methods: {}
	};
</script>

<style lang="scss" scoped>
	.distribution {
		width: 686rpx;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		box-sizing: border-box;
		margin: 24rpx 0;

		.icon_l {
			width: 48rpx;
			height: 48rpx;
			border-radius: 50%;
			font-family: PingFang SC, PingFang SC;
			font-size: 24rpx;
			color: #FFFFFF;
			line-height: 48rpx;
			text-align: center;
		}

		.pickup_icon {
			background: #56B7F5;
		}

		.send_icon {
			background: #FF7BAC;
		}

		.info {
			width: calc(100% - 80rpx);

			.address {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #18181A;
				line-height: 33rpx;
				width: 100%;
			}

			.user {
				font-family: PingFang SC, PingFang SC;
				font-size: 24rpx;
				color: #606066;
				line-height: 28rpx;
				margin-top: 10rpx;

				.username {
					margin-right: 20rpx;
				}
			}

		}
	}
</style>