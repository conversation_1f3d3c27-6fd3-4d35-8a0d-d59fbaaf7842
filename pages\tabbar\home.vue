<template>
	<view class="view">
		<!-- <view class="homeMap">
			<map id="homeMap" style="width: 100%; height: 50vh;" :latitude="latitude" :longitude="longitude" :markers="markers" />
			<image @click="this.$fn.resetLocation(longitude, latitude, 'homeMap')" class="positioning" src="../../static/tabbar/positioning.png" mode="" />
		</view> -->
		<section class="top-banner">
			<u-image src="/static/common/homebg1.png" :fade="false" width="100%" height="330rpx" radius="24rpx" mode="aspectFill"></u-image>
		</section>

		<section class="content" style="margin-top: 16rpx;">
			<view class="top-tabs">
				<view class="top-tab" :class="tab === 0 ? 'active' : ''" @click="handleChangeTab(0)">
					<view class="bg" v-if="tab === 0"></view>
					<view class="icon">
						<u-image v-if="tab === 0" :fade="false" width="40rpx" height="40rpx" src="/static/common/travel.png" mode="aspectFill"></u-image>
						<u-image v-if="tab === 1" :fade="false" width="40rpx" height="40rpx" src="/static/common/travel-a.png" mode="aspectFill"></u-image>
					</view>
					<text>出行</text>
				</view>
				<view class="top-tab" :class="tab === 1 ? 'active' : ''" @click="handleChangeTab(1)">
					<view class="bg right" v-if="tab === 1"></view>
					<view class="icon">
						<u-image v-if="tab === 0" :fade="false" width="40rpx" height="40rpx" src="/static/common/feed.png" mode="aspectFill"></u-image>
						<u-image v-if="tab === 1" :fade="false" width="40rpx" height="40rpx" src="/static/common/feed-a.png" mode="aspectFill"></u-image>
					</view>
					<text>喂养</text>
				</view>
			</view>
		</section>

		<section class="pages" :style="{ borderRadius: tab === 1 ? '36rpx 0 36rpx 36rpx' : '0 36rpx 36rpx 36rpx' }">
			<view class="main">
				<!-- 出行 -->
				<template v-if="tab === 0">
					<view class="distribution" @click="$fn.jumpPage('/pages/home/<USER>/fillInInformation?type=1')">
						<view class="icon_l pickup_icon">接</view>
						<view class="info" v-if="pickAdrs.address">
							<view class="address">{{ pickAdrs.address }}</view>
							<view class="address">{{ pickAdrs.house }}</view>
							<view class="user">
								<text class="username">{{ pickAdrs.name }}</text> <text>{{ pickAdrs.phone }}</text>
							</view>
						</view>
						<view class="info" v-else>
							<text class="placeholder">请选择起始地</text>
						</view>
						<image class="icon_r" src="../../static/tabbar/goNext.png" mode="" />
					</view>
					<view class="distribution send" @click="$fn.jumpPage('/pages/home/<USER>/fillInInformation?type=2')">
						<view class="icon_l send_icon">送</view>
						<view class="info" v-if="sendAdrs.address">
							<view class="address">{{ sendAdrs.address }}</view>
							<view class="address">{{ sendAdrs.house }}</view>
							<view class="user">
								<text class="username">{{ sendAdrs.name }}</text> <text>{{ sendAdrs.phone }}</text>
							</view>
						</view>
						<view class="info" v-else>
							<text class="placeholder">请选择目的地</text>
						</view>
						<image class="icon_r" src="../../static/tabbar/goNext.png" mode="" />
					</view>
				</template>

				<!-- 喂养 -->
				<template v-if="tab === 1">
					<view class="distribution" @click="$fn.jumpPage('/pages/home/<USER>/fillInInformation?type=1')">
						<!-- <view class="icon_l pickup_icon">接</view> -->
						<view class="icon_l pickup_icon nobg">
							<u-icon name="map" size="45rpx" color="#28303F"></u-icon>
						</view>
						<view class="info" v-if="feedAdrs.address">
							<view class="address">{{ feedAdrs.address }}</view>
							<view class="address">{{ feedAdrs.house }}</view>
							<view class="user">
								<text class="username">{{ feedAdrs.name }}</text> <text>{{ feedAdrs.phone }}</text>
							</view>
						</view>
						<view class="info" v-else>
							<text class="placeholder">请选择地址</text>
						</view>
						<image class="icon_r" src="../../static/tabbar/goNext.png" mode="" />
					</view>
					<view class="distribution send" @click="orderNow">
						<!-- <view class="icon_l send_icon">送</view> -->
						<view class="icon_l send_icon nobg">
							<u-icon name="calendar" size="45rpx" color="#28303F"></u-icon>
						</view>
						<view class="info" v-if="feedDate">
							<view class="address">{{ feedDate }}</view>
						</view>
						<view class="info" v-else>
							<text class="placeholder">请选择时间</text>
						</view>
						<image class="icon_r" src="../../static/tabbar/goNext.png" mode="" />
					</view>
				</template>
				<!-- 向下箭头 -->
				<image class="arrow-down" src="/static/common/line.png" mode="aspectFill"></image>

				<!-- 下单按钮 -->
				<view class="orderBtn">
					<button class="pinkbtn mt40" @click="orderNow">为爱宠下单</button>
				</view>

				<!-- <image class="sendProcess" src="../../static/tabbar/sendProcess.png" mode=""></image> -->
			</view>
		</section>
		<section class="bottom-banner" style="margin-top: 20rpx;">
			<u-image v-if="tab === 0" :fade="false" src="/static/common/chuxing.png" mode="aspectFill" width="710rpx" height="324rpx"></u-image>
			<u-image v-if="tab === 1" :fade="false" src="/static/common/weiyang.png" mode="aspectFill" width="710rpx" height="324rpx"></u-image>
		</section>

		<c-dateTime-picker v-if="orderTimeShow" :city="myCity" :show.sync="orderTimeShow" @confirm="orderTimeConfirm"></c-dateTime-picker>
		<c-dateTime-picker v-if="timePickerShow" :city="myCity" :show.sync="timePickerShow" @confirm="orderTimeConfirm"></c-dateTime-picker>


	</view>
</template>

<script>
// var amapFile = require('../../utils/amap-wx.130.js');
// var myAmapFun = new amapFile.AMapWX({
// 	key: '37e31a3a5c07fe8b20c1bfdd3a9106f9'
// });
export default {
	data() {
		return {
			id: 0, // 使用 marker点击事件 需要填写id
			title: 'map',
			latitude: 30.64,
			longitude: 104.05,
			markers: [{
				id: 1,
				latitude: 30.64,
				longitude: 104.05,
				iconPath: '../../static/tabbar/coordinates.png',
				width: 16,
				height: 30,
				callout: {
					content: '',
					borderRadius: 10,
					bgColor: '#FF80B5',
					display: 'ALWAYS',
					color: '#fff',
					padding: '5'
				}
			}],

			// 接送信息
			pickAdrs: {},
			sendAdrs: {},

			// 喂养信息
			feedAdrs: {},
			feedDate: '',

			orderTimeShow: false,
			orderTime: '',
			myCity: '',
			tab: 0,
			orderType: 0,

			// 时间选择器相关
			timePickerShow: false,
			timePickerValue: Date.now(),
			minDate: Date.now(),
			maxDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).getTime(), // 一年后

			// 时间格式化器配置
			formatter: {
				year: '年',
				month: '月',
				day: '日',
				hour: '时',
				minute: '分'
			}
		}
	},
	onLoad(options) {
		console.log(options, "地址栏参数")
		if (options.q) {
			// 解码 q 参数（包含完整URL）
			const fullUrl = decodeURIComponent(options.q);
			console.log('完整二维码URL:', fullUrl);

			// 解析URL获取查询参数
			const inviteUserId = fullUrl.split("=")[1]

			console.log('邀请用户ID:', inviteUserId);

			if (inviteUserId) {
				wx.setStorageSync('inviteUserId', inviteUserId);
				// 这里可以添加处理邀请逻辑
			}
		}
		this.getLocationFn();
	},
	onShow() {
		if (uni.getStorageSync('refreshHome')) {
			this.pickAdrs = {}
			this.sendAdrs = {}
			uni.removeStorageSync('refreshHome')
		}

		this.getLocationFn()
		this.getAddress()
	},
	methods: {
		handleChangeTab(i) {
			this.tab = i;
			this.orderType = i;
		},

		// 获取定位
		getLocationFn() {
			uni.getLocation({
				type: 'gcj02',
				altitude: true,
				isHighAccuracy: true,
				success: (res) => {
					console.log(res, '当前位置');
					this.latitude = res.latitude
					this.longitude = res.longitude
					this.markers[0].latitude = res.latitude
					this.markers[0].longitude = res.longitude
					let location = [res.longitude, res.latitude].join(',')
					this.$amapFun.getRegeo({
						location,
						success: (data) => {
							//成功回调
							console.log(data, '我的位置信息');
							this.markers[0].callout.content = data[0].name
							this.myCity = data[0].regeocodeData.addressComponent.city
							uni.setStorageSync('city', data[0].regeocodeData.addressComponent
								.city)
						},
						fail: (info) => {
							//失败回调
							console.log(info)
							if (info.errCode == '10044') {
								uni.showToast({
									icon: 'none',
									title: '用户每日查询超过限制'
								})
							}
						}
					})
				},
				fail: (err) => {
					this.$fn.getSetting()
					console.log('我没办法获取定位', err);
				}
			});
		},
		// 获取本地存储的地址
		getAddress() {
			const pickAddress = uni.getStorageSync('pickAddress')
			if (pickAddress) {
				this.pickAdrs = pickAddress
				this.feedAdrs = pickAddress
				uni.removeStorageSync('pickAddress')
			}
			const sendAddress = uni.getStorageSync('sendAddress')
			if (sendAddress) {
				this.sendAdrs = sendAddress
				this.feedAdrs = sendAddress
				uni.removeStorageSync('sendAddress')
			}
		},
		async orderNow() {
			try {
				// 出行
				if (this.orderType == 0) {
					if (!this.pickAdrs.address) {
						return uni.showToast({
							icon: 'none',
							title: "请选择起始地"
						})
					} else if (!this.sendAdrs.address) {
						return uni.showToast({
							icon: 'none',
							title: "请选择目的地"
						})
					}
				}
				if (this.orderType == 1) {
					if (!this.feedAdrs.address) {
						return uni.showToast({
							icon: 'none',
							title: "请选择地址"
						})
					}
				}
				console.log('sendAdrs', this.pickAdrs);
				const cityRegex = /^(?:.*?(省|自治区|直辖市))?(.*?(市|自治州|地区|盟|县|区|州))/;
				const match = this.pickAdrs.address.match(cityRegex);
				let city = "";
				if (match) {
					city = match[2]; // 第2个分组是市级信息
				}
				console.log('city', city);
				this.myCity = city
				uni.setStorageSync('city', city)

				const res = await this.$api.getCityOrderTime({ city })
				console.log(res, 'res');

				// 出行
				if (this.orderType == 0) {
					this.orderTimeShow = true
				}
				// 喂养
				if (this.orderType == 1) {
					if (!this.feedAdrs.address) {
						return uni.showToast({
							icon: 'none',
							title: "请选择地址"
						})
					}
					this.timePickerShow = true
				}

			} catch (error) {
				if (error.code == 500 && error.data == null) {
					uni.showToast({
						icon: 'none',
						title: error.msg
					})
				}
			}



		},

		// 立即下单
		orderTimeConfirm(v) {
			this.orderTimeShow = false
			this.timePickerShow = false
			// console.log(v, '下单时间');
			// orderType: 0, // 订单类型(0=出行,1=喂养)
			if (this.orderType == 0) {
				this.$fn.jumpPage(
					`../home/<USER>/confirmOrder?date=${JSON.stringify(v)}&pickAdrs=${JSON.stringify(this.pickAdrs)}&sendAdrs=${JSON.stringify(this.sendAdrs)}&orderType=${this.orderType}`
				)
			} else {
				this.feedDate = v.label
				// 喂养模式 - 跳转到喂养订单页面
				this.$fn.jumpPage(`/pages/home/<USER>/feedingOrder?date=${JSON.stringify(v)}&feedAdrs=${JSON.stringify(this.feedAdrs)}&orderType=${this.orderType}`);
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.view {
	min-height: 100vh;
	padding: 24rpx;
	background: #F7F3F6;

	.mt40 {
		margin-top: 40rpx;
	}

	.homeMap {
		position: relative;

		.positioning {
			width: 80rpx;
			height: 80rpx;
			position: absolute;
			right: 40rpx;
			bottom: 90rpx;
		}
	}

	.top-banner {}

	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		.top-tabs {
			margin-top: 8rpx;
			display: flex;
			background: #ffffff60;
			border-radius: 40rpx 40rpx 0 0;
			padding: 24rpx 0 16rpx 0;
			position: relative;
			z-index: 99;

			.top-tab {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 400;
				font-size: 32rpx;
				color: #6b7280;
				position: relative;
				z-index: 99;

				.bg {
					position: absolute;
					top: -38rpx;
					left: 0;
					width: 100%;
					height: 180rpx;
					background: #fff;
					padding: 30rpx 0;
					border-radius: 40rpx 40rpx 0 0;
					z-index: -1;
					overflow: visible;

					&::after {
						content: "";
						position: absolute;
						top: 0;
						right: -36rpx;
						width: 180rpx;
						height: 100%;
						background: #fff;
						transform: skewX(18deg);
						border-top-right-radius: 40rpx;
						border-bottom-right-radius: 0;
					}
				}

				.bg.right {
					&::after {
						left: -36rpx;
						right: auto;
						transform: skewX(-18deg);
						border-top-left-radius: 40rpx;
						border-top-right-radius: 0;
						border-bottom-left-radius: 0;
					}
				}

				.icon {
					margin-right: 8rpx;
				}

				&.active {
					color: #000000;
					font-weight: 600;
					font-size: 32rpx;

					&::after {
						content: "";
						position: absolute;
						bottom: -16rpx;
						left: 50%;
						transform: translateX(-50%) skewX(-22deg);
						width: 120rpx;
						height: 14rpx;
						background: #ff80b5;
						border-radius: 999rpx;
					}
				}
			}
		}
	}

	.pages {
		background-color: #ffffff;
		border-radius: 36rpx;


		.main {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 0 40rpx;
			padding-bottom: 40rpx;
			background: #ffffff60;
			position: relative;

			.distribution {
				width: 100%;
				height: 112rpx;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				gap: 14rpx;
				box-sizing: border-box;
				margin-top: 20rpx;

				.icon_l {
					width: 48rpx;
					height: 48rpx;
					border-radius: 8rpx;
					font-family: PingFang SC, PingFang SC;
					font-size: 24rpx;
					color: #FFFFFF;
					line-height: 48rpx;
					text-align: center;
				}

				.pickup_icon {
					color: #0F6EFF;
					background: #EEF6FF;
					position: relative;

					&::after {
						content: '';
						position: absolute;
						width: 12rpx;
						height: 12rpx;
						left: -36rpx;
						top: 50%;
						transform: translateY(-50%);
						background: #0F6EFF;
						border-radius: 50%;
					}
				}

				.send_icon {
					color: #FF80B5;
					background: #FFECF4;
					position: relative;

					&::after {
						content: '';
						position: absolute;
						width: 12rpx;
						height: 12rpx;
						left: -36rpx;
						top: 50%;
						transform: translateY(-50%);
						background: #FF80B5;
						border-radius: 50%;
					}
				}

				.nobg {
					width: 48rpx;
					height: auto;
					background: none;
				}

				.info {
					width: 80%;

					.address {
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 28rpx;
						color: #18181A;
						line-height: 33rpx;
						width: calc(100% - 20rpx);
						white-space: nowrap;
						/* 保持文本在一行显示 */
						overflow: hidden;
						/* 隐藏超出容器的部分 */
						text-overflow: ellipsis;
						/* 超出部分显示省略号 */
					}

					.user {
						font-family: PingFang SC, PingFang SC;
						font-size: 24rpx;
						color: #606066;
						line-height: 28rpx;
						margin-top: 10rpx;

						.username {
							margin-right: 20rpx;
						}
					}

					.placeholder {
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 36rpx;
						color: #18181A;
					}
				}



				.icon_r {
					width: 12rpx;
					height: 20rpx;
				}

			}

			.arrow-down {
				position: absolute;
				top: 32%;
				left: 7.2%;
				transform: translate(-50%, -50%);
				width: 10rpx;
				height: 110rpx;
			}
		}
	}

	.orderBtn {
		width: 630rpx;
	}

	// .sendProcess {
	// 	width: 686rpx;
	// 	height: 244rpx;
	// 	margin-top: 50rpx;
	// }
}
</style>