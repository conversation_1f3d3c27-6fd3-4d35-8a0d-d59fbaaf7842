<template>
	<!-- 收货地址 -->
	<view>
		<view class="main">
			<!-- 	<view class="oneBox" v-for="(v,i) in addressList" :key="v.id">
				<view class="">
					<view class="df aic">
						<view class="name">{{v.consignee}}</view>
						<view class="tel">{{v.phone}}</view>
						<view class="default" v-if="v.is_default==1">默认</view>
					</view>
					<view class="addres">
						{{v.address}}
					</view>
				</view>
				<view class="df">
					<view class="editor" @click="jumpEditAddress(v)">编辑</view>
					<view class="del" @click="delAddrees(v)">删除</view>
				</view>
			</view> -->


			<view class="address_item" v-for="item in addressList" :key="item">
				<view class="surname">
					{{item.name?item.name.substring(0,1):''}}
				</view>
				<view class="content">
					<view class="info">
						<view class="address_name single-line-ellipsis">
							{{item.address}}
						</view>
						<view class="address_name single-line-ellipsis">
							{{item.house}}
						</view>
						<view class="user">
							<text class="username">{{item.name}}</text> <text>{{item.phone}}</text>
						</view>
					</view>

					<view class="btns">
						<text class="btn1" @click="jumpEditAddress(item)">编辑</text>
						<text class="btn2" @click="delAddressFn(item.id)">删除</text>
					</view>

				</view>
			</view>
		</view>

		<view class="bottom_btn" @click="jumpEditAddress(null)">
			<view class="btn">
				<!-- +添加收货地址 -->
				<button class="pinkbtn">+添加收货地址</button>
			</view>

		</view>
		<view v-if="addressList.length==0" class="no-data">
			<image src="/static/common/noContent.png" mode=""></image>
			无收货地址
		</view>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				page: 1,
				showpage: 10,
				addressList: [{
					id: 1,
					phone: 13118441212,
					address: '这是地址呢地址地址四川泸州',
					consignee: '编译',
					is_default: 1,
				}, {
					id: 2,
					phone: 13118441212,
					address: '请另行在小程序开发工具的控制台查看',
					consignee: '编译',
					is_default: 0,
				}, {
					id: 3,
					phone: 13118441212,
					address: '请另行在小程序开发工具的控制台查看',
					consignee: '编译',
					is_default: 0,
				}, ]
			}
		},
		onLoad() {

		},
		onShow() {
			this.getAddressData()
		},
		methods: {
			// 获取地址详情
			getAddressData() {
				this.$api.getAddressList({
					type: 0
				}).then(res => {
					console.log(res);
					this.addressList = res.data
				}).catch(err => {
					console.log(err.data, 'err')
					if (err.data.code == 0) {
						this.addressList = []
					}
				})
			},
			// 跳转到编辑地址
			jumpEditAddress(v) {
				uni.navigateTo({
					url: '/pages/my/pages/editAddress?v=' + JSON.stringify(v)
				})
			},
			// 删除按钮
			delAddressFn(id) {
				uni.showModal({
					title: '温馨提示',
					content: '确认删除此地址?',
					success: (res) => {
						if (res.confirm) {
							// console.log('用户点击确定');
							this.$api.delAddress({
								id
							}).then(res => {
								uni.showToast({
									title: '操作成功',
									icon: 'success'
								})
								this.getAddressData()
							})
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},
		},
	}
</script>

<style lang="scss" scoped>
	.main {
		padding: 0rpx 30rpx 180rpx;
		overflow-y: scroll;
	}

	.oneBox {
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #F0F2F5;
		padding: 40rpx 0 30rpx;

		.name {
			font-size: 28rpx;
			font-weight: bold;
			margin-right: 30rpx;
		}

		.tel {
			font-size: 24rpx;
		}

		.addres {
			margin-top: 12rpx;
			width: 500rpx;
			font-size: 24rpx;
		}

		.editor {
			color: #1183FF;
			font-size: 24rpx;
		}

		.del {
			margin-left: 20rpx;
			color: #FC3E3E;
			font-size: 24rpx;
		}

		.default {
			width: 66rpx;
			height: 32rpx;
			background: #FF802F;
			border-radius: 16rpx;
			color: #fff;
			font-size: 20rpx;
			text-align: center;
			margin-left: 20rpx;

			&.dn {
				display: none;
			}
		}
	}

	.no-data {
		margin-top: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		color: #BCBCBC;
		font-size: 28rpx;

		image {
			width: 300rpx;
			height: 140rpx;
		}
	}



	// ------------
	.address_item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 130rpx;

		.surname {
			width: 48rpx;
			height: 48rpx;
			background: #FFCCDF;
			border-radius: 50%;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 24rpx;
			color: #18181A;
			line-height: 48rpx;
			text-align: center;
		}

		.content {
			width: 88%;
			height: 130rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid #F0F0F0;

			.info {
				width: 80%;

				.address_name {
					width: 100%;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 28rpx;
					color: #18181A;
					line-height: 33rpx;
				}

				.user {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #606066;
					line-height: 28rpx;
					margin-top: 10rpx;

					.username {
						margin-right: 30rpx;
					}
				}
			}

			.btns {
				width: 20%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				line-height: 28rpx;

				.btn1 {
					color: #56B7F5;
				}

				.btn2 {
					color: #FF7BAC;
				}
			}
		}
	}
</style>