<template>
	<view class="page">
		<image class="minebg" src="../../static/tabbar/minebg.png" mode=""></image>
		<view class="mine">
			<c-navBar isBack title="个人中心" isTran isPerch></c-navBar>
			<view class="user">
				<view class="user_info">
					<image v-if="userInfo.avatar" class="userHander" :src="vuex_imgUrl+userInfo.avatar"
						mode="aspectFill" />
					<image v-else class="userHander" src="/static/tabbar/hander.png" mode="" />
					<view class="user_text">
						<view class="name">{{userInfo.nickname?userInfo.nickname:'暂无~'}}</view>
						<view class="phone">{{userInfo.phone?$fn.phoneEn(userInfo.phone):'暂无~'}}</view>
					</view>

				</view>
				<image class="edit" src="../../static/tabbar/editUser.png"
					@click="$fn.jumpPage('/pages/my/pages/information')"></image>
			</view>

			<view class="card">
				<image class="cardbg" src="../../static/tabbar/mineCard.png" mode=""></image>
				<view class="cardbox">
					<view class="">
						<view class="title">当前余额</view>
						<view class="num">{{(userInfo.balance/100).toFixed(2)}}</view>
					</view>
					<view class="topup" @click="$fn.jumpPage('/pages/my/pages/topUp')">
						充值
					</view>
				</view>
			</view>
		</view>

		<view class="list">
			<view class="item" v-for="item in optionList" :key="item.icon" @click="$fn.jumpPage(item.url)" v-if="item.title !== '我的分销' || inviteShow">
				<view class="info">
					<image class="icon" :src="item.icon" mode=""></image>
					<view class="">{{item.title}}</view>
				</view>
				<image class="go" src="../../static/common/go.png" mode=""></image>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inviteShow:false,
				userInfo: {
					avatar: '',
					balance: 0,
					nickname: '',
					phone: ''
				},
				optionList: [{
					icon: '../../static/tabbar/mine-op1.png',
					title: '流水记录',
					url: '/pages/my/pages/flowRecord'
				},
				{
					icon: '../../static/tabbar/mine-op7.png',
					title: '我的分销',
					url: '/pages/my/pages/myDistribution'
				},
				{
					icon: '../../static/tabbar/mine-op2.png',
					title: '我的优惠券',
					url: '/pages/my/pages/myCoupon'
				}, {
					icon: '../../static/tabbar/mine-op3.png',
					title: '常用地址',
					url: '/pages/my/pages/address'
				}, {
					icon: '../../static/tabbar/mine-op6.png',
					title: '宠物管理',
					url: '/pages/my/pages/petManagement'
				}, {
					icon: '../../static/tabbar/mine-op4.png',
					title: '使用协议',
					url: '/pages/my/pages/settings'
				}, {
					icon: '../../static/tabbar/mine-op5.png',
					title: '关于我们',
					url: '/pages/tabbar/agreement?type=4'
				}]
			}
		},
		onLoad() {

		},
		onShow() {
			if (uni.getStorageSync('token')) {
				this.getUserInfoFn()
			}
		},
		methods: {
			getUserInfoFn() {
				this.$api.getUserInfo().then(res => {
					console.log(res, '信息');
					this.userInfo = res.data
					this.inviteShow = res.data.isDistribution
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		min-height: 100vh;
		position: relative;
	}

	.minebg {
		width: 100%;
		height: 634rpx;
	}

	.mine {
		width: 100%;
		height: 634rpx;
		position: absolute;
		left: 0;
		top: 0;
	}

	.user {
		margin: 55rpx 32rpx 60rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.user_info {
			display: flex;
			align-items: center;
			gap: 28rpx;

			.userHander {
				width: 160rpx;
				height: 160rpx;
				border-radius: 50%;
			}

			.user_text {

				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 48rpx;
					color: #FFFFFF;
					line-height: 56rpx;
					;
				}

				.phone {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: 38rpx;
					margin-top: 44rpx;
				}

			}

		}

		.edit {
			width: 36rpx;
			height: 36rpx;
			margin-right: 8rpx;
		}

	}

	.card {
		position: absolute;
		width: 100%;
		bottom: 0;
	}

	.cardbg {
		width: 702rpx;
		height: 192rpx;
		margin: 0 auto;
		display: block;
	}

	.cardbox {
		width: 702rpx;
		height: 192rpx;
		position: absolute;
		top: 0;
		left: 50%;
		transform: translate(-50%);
		padding: 32rpx 24rpx 42rpx 40rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #303033;
			line-height: 28rpx;
		}

		.num {
			font-family: D-DIN Exp-DINExp, D-DIN Exp-DINExp;
			font-weight: bold;
			font-size: 64rpx;
			color: #18181A;
			line-height: 75rpx;
			margin-top: 20rpx;
		}

		.topup {
			width: 128rpx;
			height: 64rpx;
			background: #FF7BAC;
			box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(255, 59, 114, 0.2);
			border-radius: 32rpx 32rpx 32rpx 32rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
			line-height: 64rpx;
			text-align: center;
		}

	}

	.list {
		padding: 0 32rpx;
		box-sizing: border-box;

		.item {
			width: 100%;
			padding: 40rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid #EBEBF0;

			.info {
				display: flex;
				align-items: center;
				gap: 14rpx;

				.icon {
					width: 32rpx;
					height: 32rpx;
				}

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 32rpx;
					color: #48484D;
					line-height: 38rpx;
				}

			}

			.go {
				width: 28rpx;
				height: 28rpx;
			}

		}

	}
</style>