<template>
	<view class="container">
		<view class="star-icon">
			<image class="icon" src="@/pages/my/static/distribution-icon1.png" mode="" />
		</view>
		<!-- 余额 -->
		<view class="balance">
			<image class="bg" src="@/pages/my/static/distribution-icon2.png" mode="" />
			<view class="contet">
				<view class="left-box">
					<view class="title">
						我的余额
					</view>
					<view class="number">
						{{(balance/100).toFixed(2)}}
					</view>
				</view>
				<view class="right-box">
					<view class="btn" @click="handleMoney">
						提现
					</view>
				</view>
			</view>

		</view>
		<!-- 余额 -->
		<!-- 二维码 -->
		<view class="code">
			<image class="bg" src="@/pages/my/static/distribution-icon3.png" mode="" />
			<view class="contet">
				<view class="title">
					<span class="text1">邀请你</span>
					<span class="text2">成为推广员</span>
				</view>
				<view class="code-img-bg">
					<image show-menu-by-longpress="true" v-if="codeUrl" class="code-img"
						:src="codeUrl" mode="" />
				</view>
				<view class="save-box">
					<image class="icon" src="@/pages/my/static/distribution-icon4.png" mode="" />
					<view class="text">
						长按保存图片到手机
					</view>
				</view>
			</view>
		</view>
		<!-- 二维码 -->
		<!-- 文案区域 -->
		<view class="textarea-box">
			{{text}}
		</view>
		<!-- 文案区域 -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				balance: 0.00,
				codeUrl: "",
				text: ''
			};
		},
		onShow() {
			this.getInviteCommissionDetail()
		},
		methods: {
			getInviteCommissionDetail() {
				this.$api.getInviteCommissionDetail().then(res => {
					console.log(res, '邀请分佣详情页面');
					this.balance = res.data.commissionBalance
					this.codeUrl = res.data.inviteCodeUrl
					this.text = res.data.description
				})
			},
			handleMoney() {
				this.$fn.jumpPage("/pages/my/pages/withdrawMoney?commissionBalance="+this.balance)
			}
		}
	}
</script>

<style lang="scss">
	.container {
		height: 100vh;
		background: linear-gradient(180deg, #FED3ED 0%, rgba(216, 216, 216, 0) 100%);
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		position: relative;

		.star-icon {
			position: absolute;
			right: 92rpx;
			top: 32rpx;
			z-index: 1;

			.icon {
				width: 156rpx;
				height: 160rpx;

			}
		}

		.balance {
			top: 82rpx;
			left: 50%;
			transform: translateX(-50%);
			position: absolute;
			z-index: 2;
			width: 644rpx;
			height: 200rpx;

			.bg {
				position: absolute;
				width: 644rpx;
				height: 200rpx;
				z-index: 1;
			}

			.contet {
				position: absolute;
				z-index: 2;
				width: 644rpx;
				height: 200rpx;
				display: flex;
				justify-content: space-between;
				padding: 42rpx 24rpx 40rpx 44rpx;
				box-sizing: border-box;

				.left-box {
					.title {
						font-size: 32rpx;
						color: #1D1D1D;
					}

					.number {
						margin-top: 10rpx;
						font-size: 60rpx;
						color: #000000;
						font-weight: 700;
					}
				}

				.right-box {
					display: flex;
					align-items: flex-end;

					.btn {
						font-size: 28rpx;
						color: #FFFFFF;
						width: 144rpx;
						height: 64rpx;
						line-height: 64rpx;
						text-align: center;
						background: #FF7BAC;
						border-radius: 50rpx 50rpx 50rpx 50rpx;
					}
				}
			}

		}

		.code {
			top: 324rpx;
			left: 50%;
			transform: translateX(-50%);
			position: absolute;
			z-index: 2;
			width: 688rpx;
			height: 864rpx;

			.bg {
				position: absolute;
				width: 688rpx;
				height: 864rpx;
				z-index: 1;
			}

			.contet {
				position: absolute;
				z-index: 2;
				width: 688rpx;
				height: 200rpx;
				padding: 80rpx 0 0 0;

				.title {
					font-size: 44rpx;
					text-align: center;
					font-weight: 700;

					.text1 {
						color: #000000;
					}

					.text2 {
						color: #DC5B86;
					}
				}

				.code-img-bg {
					background: #FFFFFF;
					width: 344rpx;
					height: 344rpx;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
					margin: 58rpx auto;

					.code-img {
						width: 344rpx;
						height: 344rpx;
						border-radius: 20rpx 20rpx 20rpx 20rpx;
					}
				}

				.save-box {

					display: flex;
					align-items: center;
					justify-content: center;
					margin-top: 170rpx;

					.icon {
						width: 56rpx;
						height: 56rpx;
					}

					.text {
						margin-left: 14rpx;
						font-size: 32rpx;
						color: #FF6AA2;
					}
				}
			}
		}

		.textarea-box {
			top: 1274rpx;
			left: 50%;
			transform: translateX(-50%);
			position: absolute;
			z-index: 2;
			width: 688rpx;
			text-align: center;
			font-size: 32rpx;
			color: #909090;
		}
	}
</style>