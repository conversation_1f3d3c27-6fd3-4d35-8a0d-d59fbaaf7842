<template>
	<view class="page">
		<!-- <c-navBar title="支付成功" isPerch></c-navBar> -->
		<view class="main">
			<image class="icon" src="../static/paySuccessIcon.png" mode="" />
			<view class="text">成功充值 {{price?price:'0'}}</view>
			<view class="price">-￥{{payAmount}}</view>
			<view class="back" @click="$fn.jumpBack"><button class="pinkbtn">返回</button></view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "paymentSuccess",
		data() {
			return {
				price: 0,
				payAmount: '0.00'
			};
		},
		onLoad(option) {
			if (option.price) {
				this.price = Number(option.price)
			}
			if (option.payAmount) {
				this.payAmount = option.payAmount
			}
		},
		onShow() {},
		methods: {

		}
	};
</script>

<style lang="scss" scoped>
	.main {
		margin-top: 10vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 50rpx;

		.icon {
			width: 240rpx;
			height: 240rpx;
		}

		.text {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #18181A;
			line-height: 36rpx;
		}

		.price {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #909099;
			line-height: 28rpx;
		}

		.back {
			width: 360rpx;
			height: 88rpx;
		}

	}
</style>