<template>
	<view class="page">
		<c-navBar title="司机位置" isPerch></c-navBar>
		<view class="homeMap">
			<map id="homeMap" :scale="12"
				:style="'width: 100%; height: calc(100vh - 480rpx - '+(statusHeight+titleHeight)+'px);'"
				:latitude="latitude" :longitude="longitude" :markers="markers" />
			<!-- <image @click="this.$fn.resetLocation(longitude,latitude,'homeMap')" class="positioning"
				src="../../../static/tabbar/positioning.png" mode=""> -->
		</view>

		<view class="main">
			<view class="driver">
				<view class="driver_info">
					<image class="avatar" :src="tabData.avatar?vuex_imgUrl+tabData.avatar:'/static/tabbar/hander.png'"
						mode=""></image>
					<view class="info">
						<view class="name">{{tabData.name}}</view>
						<view class="phone">{{$fn.phoneEn(tabData.phone)}}</view>
					</view>
				</view>

				<view class="features">
					<view class="features_item" @click="callPhone(tabData.phone)">
						<image class="features_icon" src="../static/cell.png" mode=""></image>
					</view>
				</view>
			</view>

			<view class="address" @click="openAddress(tabData.position)">
				<image class="address_icon" src="../static/navigation.png" mode=""></image>
				<view class="address_info">
					<view class="name">{{desc}}</view>
					<view class="detail">{{addressName}}</view>
				</view>
			</view>
			<view class="footer">
				<button class="pinkbtn" @click="lookVideo">查看视频</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "Name",
		data() {
			return {
				orderId: '',
				desc: '',
				addressName: '',
				statusHeight: 0,
				titleHeight: 0,
				id: 0, // 使用 marker点击事件 需要填写id
				title: 'map',
				latitude: 30.64,
				longitude: 104.05,
				markers: [{
						id: 1,
						latitude: 30.64,
						longitude: 104.05,
						iconPath: '../static/car.png',
						width: 25,
						height: 38,
						callout: {
							content: '武侯区人民政府(武候祠大街南)',
							borderRadius: 10,
							bgColor: '#FF80B5',
							display: 'ALWAYS',
							color: '#fff',
							padding: '5'
						}
					},
					{
						id: 2,
						latitude: 30.64,
						longitude: 104.051,
						iconPath: '../static/startPoint.png',
						width: 20,
						height: 20,
					},
					{
						id: 3,
						latitude: 30.64,
						longitude: 104.052,
						iconPath: '../static/endPoint.png',
						width: 20,
						height: 20,
					}
				],
				tabData: {
					avatar: "",
					carNumber: "",
					deviceId: "",
					driverId: '',
					name: "",
					orderId: '',
					orderStatus: '',
					phone: "",
					position: {
						lastQueryPositionTime: null,
						latitude: "",
						longitude: ""
					}
				},
				timer:null
			};
		},
		onLoad(option) {
			this.orderId = option.orderId
			this.getPhe()
			this.getOrderCarPositionFn(this.orderId)
			this.timer = setInterval(() => {
				this.getOrderCarPositionFn(this.orderId)
			}, 10000)
		},
		onShow() {},
		onUnload() {
			clearInterval(this.timer)
		},
		methods: {
			// 获取手机导航栏高度
			getPhe() {
				// 状态栏高度
				let systemInfo = uni.getSystemInfoSync()
				this.statusHeight = systemInfo.safeArea.top
				// app端的标题栏高度
				this.titleHeight = 46
				// 小程序端的标题栏的高度
				// #ifdef MP-WEIXIN
				let titleSize = wx.getMenuButtonBoundingClientRect()
				this.titleHeight = titleSize.height + (titleSize.top - systemInfo.safeArea.top) * 2
				// #endif
			},


			// 查询信息
			getOrderCarPositionFn(orderId) {
				this.$api.getOrderCarPosition({
					lastTime: this.tabData.position.lastQueryPositionTime,
					orderId
				}).then(res => {
					console.log(res, 'res');

					if (!res.data.position.longitude || !res.data.position.latitude) return

					this.tabData = res.data
					let location = [this.tabData.position.longitude, this.tabData.position.latitude].join(',')
					// 地图中心
					this.latitude = this.tabData.position.latitude
					this.longitude = this.tabData.position.longitude
					// 司机位置
					this.markers[0].latitude = this.tabData.position.latitude
					this.markers[0].longitude = this.tabData.position.longitude
					// 起点
					this.markers[1].latitude = this.tabData.sendLat
					this.markers[1].longitude = this.tabData.sendLng
					// 终点
					this.markers[2].latitude = this.tabData.takeLat
					this.markers[2].longitude = this.tabData.takeLng
					this.$amapFun.getRegeo({
						location,
						success: (data) => {
							//成功回调
							console.log(data, '我的位置信息');
							this.markers[0].callout.content = data[0].desc
							this.desc = data[0].desc
							this.addressName = data[0].name
						},
						fail: (info) => {
							//失败回调
							console.log(info)
							if (info.errCode == '10044') {
								uni.showToast({
									icon: 'none',
									title: '用户每日查询超过限制'
								})
							}
						}
					})
				})
			},


			// 拨打电话
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},
			// 打开定位
			openAddress(v) {
				uni.openLocation({
					address: this.addressName,
					name: this.desc,
					latitude: Number(v.latitude),
					longitude: Number(v.longitude)
				})
			},

			//查看视频
			lookVideo() {
				uni.navigateTo({
					url: "/pages/orderList/pages/driverVideo?orderId=" + this.orderId
				})
			},

		}
	};
</script>

<style lang="scss" scoped>
	.homeMap {
		position: relative;

		.positioning {
			width: 80rpx;
			height: 80rpx;
			position: absolute;
			right: 40rpx;
			bottom: 90rpx;
		}
	}

	.main {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 750rpx;
		height: 520rpx;
		background: #fff;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
	}


	.driver {
		padding: 32rpx;
		box-sizing: border-box;
		margin-bottom: 10rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;

		.driver_info {
			display: flex;
			align-items: center;
			gap: 24rpx;

			.avatar {
				width: 112rpx;
				height: 112rpx;
				border-radius: 80rpx 80rpx 80rpx 80rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				gap: 22rpx;

				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 32rpx;
					color: #18181A;
					line-height: 38rpx;
				}

				.phone {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #606066;
					line-height: 28rpx;
				}

			}

		}

		.features {
			display: flex;
			align-items: center;
			gap: 40rpx;

			.features_item {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 12rpx;

				.features_icon {
					width: 36rpx;
					height: 36rpx;
				}
			}

		}
	}

	.address {
		margin: 0 32rpx;
		background: #F7F7FA;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		display: flex;
		align-items: center;
		gap: 20rpx;
		padding: 24rpx 16rpx;
		box-sizing: border-box;

		.address_icon {
			width: 48rpx;
			height: 48rpx;
		}

		.address_info {
			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #18181A;
				line-height: 33rpx;
			}

			.detail {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #606066;
				line-height: 28rpx;
				margin-top: 10rpx;
			}

		}
	}

	.footer {
		width: 686rpx;
		height: 96rpx;
		margin: 36rpx auto;
	}
</style>