<template>
	<view class="page">
		<view class="headBg">
			<c-navBar title="确认下单" isTran isPerch :background="ceiling?'rgb(253, 220, 232)':''"></c-navBar>

			<view class="card">
				<view class="distribution" @click="$fn.jumpPage('/pages/home/<USER>/fillInInformation?type=1')">
					<view class="icon_l pickup_icon">
						接
					</view>
					<view class="info" v-if="pickAdrs.address">
						<view class="address">{{pickAdrs.address}}</view>
						<view class="address">{{pickAdrs.house}}</view>
						<view class="user">
							<text class="username">{{pickAdrs.name}}</text> <text>{{pickAdrs.phone}}</text>
						</view>
					</view>
					<view class="info" v-else>
						<text class="placeholder">请输入起始地</text>
					</view>
					<image class="icon_r" src="../../../static/tabbar/goNext.png" mode="" />
				</view>
				<view class="distribution" @click="$fn.jumpPage('/pages/home/<USER>/fillInInformation?type=2')">
					<view class="icon_l send_icon">送</view>
					<view class="info" v-if="sendAdrs.address">
						<view class="address">{{sendAdrs.address}}</view>
						<view class="address">{{sendAdrs.house}}</view>
						<view class="user">
							<text class="username">{{sendAdrs.name}}</text> <text>{{sendAdrs.phone}}</text>
						</view>
					</view>
					<view class="info" v-else>
						<text class="placeholder">请输入目的地</text>
					</view>
					<image class="icon_r" src="../../../static/tabbar/goNext.png" mode="" />
				</view>

				<view class="appointment_time">
					<view class="title">
						预约时间
					</view>
					<view class="time" @click="orderTimeShow=true">
						<text class="date">{{orderDate.label}}</text>
						<image class="icon_r" src="../../../static/tabbar/goNext.png" mode="" />
					</view>
				</view>
				<view class="discount-tip">
				  <text class="discount-text">同一订单默认2只宠物，超出将收取“多宠费”，详情咨询宠骑+骑士</text>
<!-- 				  <text class="highlight">{{timeSaleData.twelveHours}}折</text>
				  <text class="discount-text">，≥24小时享</text>
				  <text class="highlight">{{timeSaleData.twentyFourHours}}折</text> -->
				</view>
			</view>
			<view class="card">
				<view class="form bdbt">
					<view class="title">
						爱宠名称
					</view>
					<view class="centent">
						<u--input inputAlign="right" placeholder="请输入" border="none" v-model="petName" />
					</view>
				</view>
				<view class="form">
					<view class="title">
						订单备注
					</view>
					<view class="centent">
					</view>
				</view>
				<view class="">
					<u--textarea v-model="remarks" placeholder="请输入"></u--textarea>
				</view>
			</view>
			<view class="card">
				<view class="form bdbt">
					<view class="title">
						里程费
					</view>
					<view class="centent">
						￥{{(moneyData.distanceAmount/100).toFixed(2)}}
					</view>
				</view>
				<view class="form bdbt">
					<view class="title" >
						综合费<text style="font-size: 17rpx;">(含车内清洁、视频流媒体、停车费、上门费等费用)</text>
					</view>
					<view class="centent" style="width: 200rpx;">
						￥{{(moneyData.visitAmount/100).toFixed(2)}}
					</view>
				</view>
				<view class="form bdbt"
					@click="$fn.jumpPage(`/pages/home/<USER>/selectCoupon?city=${myCity}&orderPrice=${moneyData.payAmount+coupon.amount}`)">
					<view class="title">
						优惠券
					</view>
					<view class="centent">
						<text class="coupon"
							:class="coupon.amount?'coupon_have':''">{{coupon.amount?'-￥'+(coupon.amount/100).toFixed(2):'选择优惠券'}}</text>
						<image class="icon_r" src="../../../static/tabbar/goNext.png" mode="" />
					</view>
				</view>
<!-- 				<view class="form bdbt">
					<view class="title">
						时间折扣金额
					</view>
					<view class="centent">
						-￥{{(moneyData.timeDiscountAmount/100).toFixed(2)}}
					</view>
				</view> -->
				<view class="form">
					<view class="title" @click="descriptionShow=true">
						<text class="description">价格说明</text>
						<image class="doubt" src="../../../static/tabbar/doubt.png" mode="" />
					</view>
					<view class="centent">
						<view class="footing">合计：
							<text class="totalPrice">￥{{(moneyData.payAmount/100).toFixed(2)}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 协议 -->
			<view class="agreement-box">
				<view class="ag-box" @click="tabAgree">
					<image v-if="isAgree" src="/static/common/select.png" mode=""></image>
					<image v-else src="/static/common/noSelect.png" mode=""></image>
				</view>
				<view class="agreement">
					<text @click.stop="tabAgree">我已阅读并同意</text>
					<text class="primary-color"
						@click.stop="$fn.jumpPage(`/pages/tabbar/agreement?type=3`)">《支付协议》</text>
				</view>
			</view>

			<view class="orderNow">
				<button class="pinkbtn" @click="orderNow">立即下单</button>
			</view>
			<view class="pile"></view>
		</view>

		<!-- 价格说明 -->
		<u-popup :show="descriptionShow" :round="20" closeable @close="descriptionShow=false">
			<view class="pop">
				<view class="title">价格说明</view>
				<view class="headline">价格说明</view>
				<view class="detail">
					{{moneyData.priceInstructions}}
				</view>
				<view class="headline">计费规则</view>
				<view class="detail">
					{{moneyData.priceRule}}
				</view>
			</view>
		</u-popup>

		<c-dateTime-picker v-if="orderTimeShow" :show.sync="orderTimeShow" :city="myCity"
			@confirm="orderTimeConfirm"></c-dateTime-picker>
	</view>
</template>

<script>
	export default {
		name: "Name",
		data() {
			return {
				petName: '',
				remarks: '',
				isAgree: false,
				descriptionShow: false,
				orderTimeShow: false,
				pickAdrs: {},
				sendAdrs: {},
				orderDate: {
					label: '',
					value: ''
				},
				moneyData: {
					balance: 0, //账户余额
					distanceAmount: 0, //里程费
					payAmount: 0, //实际支付
					totalAmount: 0, //订单总金额
					visitAmount: 0, //上门费
					priceInstructions: '', //价格说明
					priceRule: '' //计价规则
				},
				timeSaleData:{
					twelveHours:'',
					twentyFourHours:''
				},
				coupon: {
					amount: 0
				}, //优惠券
				ceiling: false,
				myCity: ''
			};
		},


		onPageScroll(e) {
			console.log(e, '滚动距离');
			if (e.scrollTop > 0) {
				this.ceiling = true
			} else {
				this.ceiling = false
			}
		},
		onLoad(option) {
			// 获取参数
			let {
				date,
				pickAdrs,
				sendAdrs
			} = option
			if (date) {
				this.orderDate = JSON.parse(date)
			}
			if (pickAdrs) {
				this.pickAdrs = JSON.parse(pickAdrs)
				console.log(this.pickAdrs,'接货地址')
			}
			if (sendAdrs) {
				console.log(this.sendAdrs,'取货货地址')
				this.sendAdrs = JSON.parse(sendAdrs)
			}
			this.myCity = uni.getStorageSync('city')
		},
		onShow() {
			// 获取本地存储的地址
			this.getStorage()
			// 计算订单信息价格
			this.confirmOrderInfoFn()
			this.getTimeSale()

		},
		methods: {
			// 获取本地存储的地址
			getStorage() {
				const pickAddress = uni.getStorageSync('pickAddress')
				if (pickAddress) {
					this.pickAdrs = pickAddress
					const cityRegex = /^(?:.*?(省|自治区|直辖市))?(.*?(市|自治州|地区|盟|县|区|州))/;
					const match = this.pickAdrs.address.match(cityRegex);
					let city = "";
					if (match) {
					  city = match[2]; // 第2个分组是市级信息
					}
					this.myCity = city
					uni.removeStorageSync('pickAddress')
				}
				const sendAddress = uni.getStorageSync('sendAddress')
				if (sendAddress) {
					this.sendAdrs = sendAddress
					uni.removeStorageSync('sendAddress')
				}

				const coupon = uni.getStorageSync('coupon')
				if (coupon) {
					this.coupon = coupon
					uni.removeStorageSync('coupon')
				}
			},
			// 计算订单信息价格
			confirmOrderInfoFn() {
				if (!this.pickAdrs.lat || !this.sendAdrs.lat) return uni.showToast({
					icon: "none",
					title: "该地址无法获取经纬度"
				})
				let dateArr = this.orderDate.value.split(' ')
				let appointmentTime = this.$fn.parseTime(dateArr[0], '{y}-{m}-{d}' + ' ' + dateArr[1] + ':00')
				let form = {
					city: this.myCity,
					couponAmount: this.coupon.amount ? this.coupon.amount : 0,
					startLatAndLng: {
						lat: this.pickAdrs.lat,
						lng: this.pickAdrs.lng
					},
					endLatAndLng: {
						lat: this.sendAdrs.lat,
						lng: this.sendAdrs.lng
					},
					orderTime:appointmentTime
				}

				console.log(form, 'form');
				this.$api.confirmOrderInfo(form).then(res => {
					console.log(res, '价格信息');
					this.moneyData = res.data
				})

			},
			//查询时间折扣
			getTimeSale(){
				this.$api.timeSale().then(res => {
					console.log(res, '时间折扣信息');
					this.timeSaleData = res.data
				})
			},
			// 选择下单时间
			orderTimeConfirm(v) {
				console.log(v, '预约时间');
				this.orderDate = v
				this.confirmOrderInfoFn()
			},


			// 同意/不同意协议
			tabAgree() {
				this.isAgree = !this.isAgree
			},
			// 立即下单
			orderNow() {
				if (!this.petName) {
					return uni.showToast({
						title: '请输入宠物名称',
						icon: 'none',
						duration: 2000
					});
				}
				if (!this.pickAdrs.address) {
					return uni.showToast({
						title: '请输入起始地',
						icon: 'none',
						duration: 2000
					});
				}
				if (!this.sendAdrs.address) {
					return uni.showToast({
						title: '请输入目的地',
						icon: 'none',
						duration: 2000
					});
				}
				if (!this.orderDate.value) {
					return uni.showToast({
						title: '请先选择预约时间',
						icon: 'none',
						duration: 2000
					});
				}

				if (!this.isAgree) {
					return uni.showToast({
						title: '请先查看并同意协议',
						icon: 'none',
						duration: 2000
					});
				}
				let dateArr = this.orderDate.value.split(' ')
				let appointmentTime = this.$fn.parseTime(dateArr[0], '{y}-{m}-{d}' + ' ' + dateArr[1] + ':00')
				console.log(new Date(appointmentTime), '预约时间');
				let params = {
					appointmentTime,
					city: this.myCity,
					couponId: this.coupon.id,
					orderType: 0,
					petName: this.petName,
					remark: this.remarks,
					sendAddress: this.pickAdrs.address + this.pickAdrs.house,
					sendName: this.pickAdrs.name,
					sendPhone: this.pickAdrs.phone,
					startLatAndLng: {
						lat: this.pickAdrs.lat,
						lng: this.pickAdrs.lng
					},
					takeAddress: this.sendAdrs.address + this.sendAdrs.house,
					takeName: this.sendAdrs.name,
					takePhone: this.sendAdrs.phone,
					endLatAndLng: {
						lat: this.sendAdrs.lat,
						lng: this.sendAdrs.lng
					},
				}


				this.$fn.jumpPage('/pages/home/<USER>/payOrder?price=' + this.moneyData.payAmount + '&params=' + JSON
					.stringify(params))
			}
		}
	};
</script>

<style lang="scss" scoped>
	.discount-tip {
	  margin-top: 16rpx;
	  padding: 0rpx 0rpx;
	  // background-color: #FFF9E6;
	  border-radius: 8rpx;
	  font-size: 24rpx;
	  line-height: 1.6;
	  color: #666;
	}
	
	.highlight {
	  color: #FF5A5F;
	  font-weight: bold;
	  margin: 0 4rpx;
	}
	.page {
		min-height: 100vh;
		background: #F4F5F7;

		.headBg {
			width: 750rpx;
			height: 320rpx;
			background: linear-gradient(360deg, rgba(255, 204, 223, 0) 0%, #FFD9E7 100%);
		}
	}

	.card {
		width: 702rpx;
		background: #FFFFFF;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		margin: 10rpx auto 20rpx;
		padding: 0 24rpx 20rpx;
		box-sizing: border-box;
	}

	.send {
		background: #FFF7FA;
	}

	.distribution {
		width: 100%;
		height: 130rpx;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #F0F0F0;

		.icon_l {
			width: 48rpx;
			height: 48rpx;
			border-radius: 50%;
			font-family: PingFang SC, PingFang SC;
			font-size: 24rpx;
			color: #FFFFFF;
			line-height: 48rpx;
			text-align: center;
		}

		.pickup_icon {
			background: #56B7F5;
		}

		.send_icon {
			background: #FF7BAC;
		}

		.info {
			width: 80%;

			.address {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 28rpx;
				color: #18181A;
				width: calc(100% - 20rpx);
				white-space: nowrap;
				/* 保持文本在一行显示 */
				overflow: hidden;
				/* 隐藏超出容器的部分 */
				text-overflow: ellipsis;
				/* 超出部分显示省略号 */
			}

			.user {
				font-family: PingFang SC, PingFang SC;
				font-size: 24rpx;
				color: #606066;
				line-height: 28rpx;
				margin-top: 10rpx;

				.username {
					margin-right: 20rpx;
				}
			}

			.placeholder {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 36rpx;
				color: #18181A;
			}
		}





	}

	.icon_r {
		width: 12rpx;
		height: 20rpx;
	}

	.appointment_time {
		height: 113rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #18181A;
			line-height: 33rpx;
			text-align: left;
		}

		.time {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 24rpx;
			color: #56B7F5;
			line-height: 28rpx;
			text-align: right;

			.date {
				margin-right: 20rpx;
			}
		}
	}

	.bdbt {
		border-bottom: 1rpx solid #F0F0F0;
	}

	.form {
		height: 96rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;


		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 28rpx;
			color: #18181A;
			line-height: 33rpx;
			text-align: left;

			.description {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #606066;
				line-height: 28rpx;
			}

			.doubt {
				width: 20rpx;
				height: 20rpx;
				margin-left: 4rpx;
			}
		}

		.centent {
			width: 260rpx;
			text-align: right;
			color: #18181A;
			font-size: 28rpx;


			.coupon {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 28rpx;
				color: #909099;
				line-height: 33rpx;
				margin-right: 20rpx;
			}

			.coupon_have {
				color: #18181A;
			}

			.footing {
				font-size: 28rpx;
				font-weight: bold;
				color: #18181A;

				.totalPrice {
					color: #FF7FB5;
				}
			}


		}
	}


	.agreement-box {
		display: flex;
		align-items: center;
		width: 702rpx;
		margin: 0 auto;

		.ag-box {
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		image {
			width: 24rpx;
			height: 24rpx;
		}

		.agreement {
			font-size: 28rpx;
			font-weight: 500;
			color: #666666;

			.primary-color {
				color: #56B7F5;
			}
		}
	}

	.orderNow {
		width: 686rpx;
		margin: 20rpx auto;
	}


	.pile {
		height: 20rpx;
	}

	.pop {
		width: 750rpx;
		height: 720rpx;
		background: #FFFFFF;
		padding: 32rpx 32rpx 0;
		box-sizing: border-box;

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #18181A;
			line-height: 42rpx;
			text-align: center;
		}

		.headline {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #18181A;
			line-height: 33rpx;
		}

		.detail {
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 24rpx;
			color: #606066;
			line-height: 40rpx;
			margin: 20rpx 0 40rpx;
		}
	}
	
</style>